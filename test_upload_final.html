<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>最终版商品上传测试工具</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 8px;
            padding: 24px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .status-card {
            border: 1px solid #d9d9d9;
            border-radius: 6px;
            padding: 16px;
            margin: 16px 0;
        }
        .status-valid {
            border-color: #52c41a;
            background-color: #f6ffed;
        }
        .status-invalid {
            border-color: #ff4d4f;
            background-color: #fff2f0;
        }
        .status-warning {
            border-color: #faad14;
            background-color: #fffbe6;
        }
        .btn {
            background: #1890ff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 4px;
            font-size: 14px;
        }
        .btn:hover {
            background: #40a9ff;
        }
        .btn-success {
            background: #52c41a;
        }
        .btn-warning {
            background: #faad14;
        }
        .btn-danger {
            background: #ff4d4f;
        }
        .form-group {
            margin: 16px 0;
        }
        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }
        .form-textarea {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            font-size: 14px;
            min-height: 200px;
            resize: vertical;
            font-family: 'Courier New', monospace;
        }
        .log-area {
            background: #001529;
            color: #fff;
            padding: 16px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 12px;
            margin: 16px 0;
        }
        .info-item {
            background: #fafafa;
            padding: 12px;
            border-radius: 4px;
            border-left: 3px solid #1890ff;
        }
        .info-label {
            font-weight: 600;
            color: #666;
            font-size: 12px;
            margin-bottom: 4px;
        }
        .info-value {
            font-size: 14px;
            color: #333;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 最终版商品上传测试工具</h1>
        <p>完全借鉴test_upload.html逻辑，通过background.js创建ZIP并上传</p>
        
        <div class="status-card status-warning">
            <h4>🔧 技术架构</h4>
            <ul style="margin: 8px 0; padding-left: 20px;">
                <li><strong>ZIP创建：</strong> 在background.js中使用本地JSZip库</li>
                <li><strong>数据传输：</strong> ZIP文件通过ArrayBuffer传递给注入脚本</li>
                <li><strong>API上传：</strong> 在店小秘页面环境中直接上传</li>
                <li><strong>认证方式：</strong> 使用页面现有的Cookie和Token</li>
            </ul>
        </div>
    </div>

    <div class="container">
        <h3>系统状态检查</h3>
        <div class="info-grid" id="systemStatus">
            <div class="info-item">
                <div class="info-label">扩展环境</div>
                <div class="info-value" id="extensionStatus">检查中...</div>
            </div>
            <div class="info-item">
                <div class="info-label">Background JSZip</div>
                <div class="info-value" id="jszipStatus">检查中...</div>
            </div>
            <div class="info-item">
                <div class="info-label">店小秘登录</div>
                <div class="info-value" id="loginStatus">检查中...</div>
            </div>
            <div class="info-item">
                <div class="info-label">系统就绪</div>
                <div class="info-value" id="readyStatus">检查中...</div>
            </div>
        </div>
    </div>

    <div class="container">
        <h3>操作面板</h3>
        <button class="btn" id="checkSystemBtn">🔍 检查系统状态</button>
        <button class="btn btn-success" id="testJSZipBtn">📦 测试Background JSZip</button>
        <button class="btn btn-warning" id="loadSampleBtn">📝 加载示例数据</button>
        <button class="btn btn-danger" id="clearLogBtn">🗑️ 清除日志</button>
    </div>

    <div class="container">
        <h3>JSON数据输入</h3>
        
        <div class="form-group">
            <label class="form-label" for="jsonData">商品JSON数据：</label>
            <div style="display: flex; gap: 10px; margin-bottom: 10px;">
                <button type="button" class="btn" id="formatJsonBtn">格式化</button>
                <button type="button" class="btn" id="compressJsonBtn">压缩</button>
                <button type="button" class="btn" id="validateJsonBtn">验证</button>
            </div>
            <textarea class="form-textarea" id="jsonData" placeholder="请在此输入JSON数据..."></textarea>
        </div>
        
        <div style="text-align: center; margin: 20px 0;">
            <button type="button" class="btn btn-success" id="uploadBtn" style="padding: 12px 30px; font-size: 16px;">🚀 开始上传</button>
        </div>
    </div>

    <div class="container">
        <h3>调试日志</h3>
        <div id="logArea" class="log-area"></div>
    </div>

    <script src="test_upload_final.js"></script>
</body>
</html>
