// 最终版商品上传测试工具

class FinalUploadTester {
    constructor() {
        this.isExtensionAvailable = false;
        this.isJSZipAvailable = false;
        this.isDxmLoggedIn = false;
        this.init();
    }

    async init() {
        this.log('最终版上传测试工具已初始化');
        this.log('技术架构：JSON → Background创建ZIP → 注入脚本上传');
        await this.checkSystemStatus();
    }

    // 检查系统状态
    async checkSystemStatus() {
        this.log('🔍 开始检查系统状态...');
        
        // 1. 检查扩展环境
        await this.checkExtensionStatus();
        
        // 2. 检查Background JSZip
        await this.checkBackgroundJSZip();
        
        // 3. 检查店小秘登录状态
        await this.checkDxmLoginStatus();
        
        // 4. 更新系统就绪状态
        this.updateReadyStatus();
    }

    // 检查扩展环境
    async checkExtensionStatus() {
        try {
            if (typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.sendMessage) {
                this.isExtensionAvailable = true;
                this.log('✅ 扩展环境可用');
                document.getElementById('extensionStatus').textContent = '✅ 可用';
                document.getElementById('extensionStatus').style.color = '#52c41a';
            } else {
                throw new Error('扩展环境不可用');
            }
        } catch (error) {
            this.isExtensionAvailable = false;
            this.log('❌ 扩展环境不可用: ' + error.message, 'error');
            document.getElementById('extensionStatus').textContent = '❌ 不可用';
            document.getElementById('extensionStatus').style.color = '#ff4d4f';
        }
    }

    // 检查Background JSZip
    async checkBackgroundJSZip() {
        try {
            if (!this.isExtensionAvailable) {
                throw new Error('扩展环境不可用');
            }

            this.log('检查Background中的JSZip库...');
            
            const result = await this.sendMessageToBackground({
                action: 'TEST_JSZIP_AVAILABILITY'
            });
            
            if (result && result.success) {
                this.isJSZipAvailable = true;
                this.log('✅ Background JSZip可用，版本: ' + (result.version || '未知'));
                document.getElementById('jszipStatus').textContent = '✅ 可用';
                document.getElementById('jszipStatus').style.color = '#52c41a';
            } else {
                throw new Error(result?.error || 'JSZip测试失败');
            }
        } catch (error) {
            this.isJSZipAvailable = false;
            this.log('❌ Background JSZip不可用: ' + error.message, 'error');
            document.getElementById('jszipStatus').textContent = '❌ 不可用';
            document.getElementById('jszipStatus').style.color = '#ff4d4f';
        }
    }

    // 检查店小秘登录状态
    async checkDxmLoginStatus() {
        try {
            if (!this.isExtensionAvailable) {
                throw new Error('扩展环境不可用');
            }

            this.log('检查店小秘登录状态...');
            
            const result = await this.sendMessageToBackground({
                action: 'GET_DIANXIAOMI_TOKEN_STATUS'
            });
            
            if (result && result.success && result.data && result.data.code === 0) {
                this.isDxmLoggedIn = true;
                const userInfo = result.data.data;
                const userName = userInfo.account || userInfo.username || userInfo.name || '未知用户';
                this.log('✅ 店小秘已登录，用户: ' + userName);
                document.getElementById('loginStatus').textContent = '✅ 已登录';
                document.getElementById('loginStatus').style.color = '#52c41a';
            } else {
                throw new Error('店小秘未登录或认证失效');
            }
        } catch (error) {
            this.isDxmLoggedIn = false;
            this.log('❌ 店小秘登录检查失败: ' + error.message, 'error');
            document.getElementById('loginStatus').textContent = '❌ 未登录';
            document.getElementById('loginStatus').style.color = '#ff4d4f';
        }
    }

    // 更新系统就绪状态
    updateReadyStatus() {
        const isReady = this.isExtensionAvailable && this.isJSZipAvailable && this.isDxmLoggedIn;
        
        if (isReady) {
            this.log('✅ 系统检查完成，所有组件就绪');
            document.getElementById('readyStatus').textContent = '✅ 就绪';
            document.getElementById('readyStatus').style.color = '#52c41a';
        } else {
            this.log('❌ 系统未就绪，请检查上述组件状态', 'error');
            document.getElementById('readyStatus').textContent = '❌ 未就绪';
            document.getElementById('readyStatus').style.color = '#ff4d4f';
        }
    }

    // 向background发送消息
    async sendMessageToBackground(message) {
        return new Promise((resolve, reject) => {
            if (!this.isExtensionAvailable) {
                reject(new Error('扩展环境不可用'));
                return;
            }

            chrome.runtime.sendMessage(message, (response) => {
                if (chrome.runtime.lastError) {
                    reject(new Error(chrome.runtime.lastError.message));
                } else {
                    resolve(response);
                }
            });
        });
    }

    // 测试Background JSZip
    async testBackgroundJSZip() {
        try {
            this.log('🧪 测试Background JSZip功能...');
            
            const testData = '{"test": "JSZip functionality", "timestamp": ' + Date.now() + '}';
            
            const result = await this.sendMessageToBackground({
                action: 'CREATE_ZIP_FILE',
                jsonData: testData
            });
            
            if (result && result.success) {
                this.log('✅ Background JSZip测试成功');
                this.log(`ZIP文件大小: ${result.size} bytes`);
                alert('Background JSZip测试成功！ZIP文件大小: ' + result.size + ' bytes');
            } else {
                throw new Error(result?.error || 'ZIP创建失败');
            }
        } catch (error) {
            this.log('❌ Background JSZip测试失败: ' + error.message, 'error');
            alert('Background JSZip测试失败: ' + error.message);
        }
    }

    // 加载示例数据
    loadSampleData() {
        const sampleData = {
            "attributes": "[{\"propName\":\"是否可用于食品接触\",\"refPid\":4010,\"pid\":1795,\"templatePid\":1261897,\"numberInputValue\":\"\",\"valueUnit\":\"\",\"vid\":\"67313\",\"propValue\":\"是\"}]",
            "categoryId": "9938",
            "shopId": "6959965",
            "productSemiManagedReq": "100",
            "sourceUrl": "https://www.amazon.com/dp/B0D2R2CGC1",
            "productName": "3in1 Cup Lid Brush MultiFunction Gap Cleaning Brush",
            "op": 1
        };
        
        document.getElementById('jsonData').value = JSON.stringify(sampleData, null, 2);
        this.log('✅ 已加载示例数据');
    }

    // 格式化JSON
    formatJson() {
        const textarea = document.getElementById('jsonData');
        try {
            const jsonObj = JSON.parse(textarea.value);
            textarea.value = JSON.stringify(jsonObj, null, 2);
            this.log('✅ JSON格式化成功');
        } catch (e) {
            this.log('❌ JSON格式错误：' + e.message, 'error');
        }
    }

    // 压缩JSON
    compressJson() {
        const textarea = document.getElementById('jsonData');
        try {
            const jsonObj = JSON.parse(textarea.value);
            textarea.value = JSON.stringify(jsonObj);
            this.log('✅ JSON压缩成功');
        } catch (e) {
            this.log('❌ JSON格式错误：' + e.message, 'error');
        }
    }

    // 验证JSON
    validateJson() {
        const jsonData = document.getElementById('jsonData').value.trim();
        
        if (!jsonData) {
            this.log('❌ 请输入JSON数据', 'error');
            return false;
        }
        
        try {
            JSON.parse(jsonData);
            this.log('✅ JSON格式验证通过');
            return true;
        } catch (e) {
            this.log('❌ JSON格式错误：' + e.message, 'error');
            return false;
        }
    }

    // 上传商品
    async uploadProduct() {
        try {
            this.log('🚀 开始商品上传流程...');
            
            // 1. 验证JSON
            const jsonData = document.getElementById('jsonData').value.trim();
            if (!this.validateJson()) {
                return;
            }

            // 2. 检查系统状态
            if (!this.isExtensionAvailable || !this.isJSZipAvailable || !this.isDxmLoggedIn) {
                this.log('❌ 系统未就绪，请先检查系统状态', 'error');
                alert('系统未就绪，请先点击"检查系统状态"确保所有组件正常');
                return;
            }

            this.log('✅ 系统状态检查通过，开始上传...');
            this.log('上传流程：JSON → Background创建ZIP → 注入脚本上传');
            
            // 3. 发送上传请求到background
            const result = await this.sendMessageToBackground({
                action: 'UPLOAD_DIANXIAOMI_PRODUCT',
                jsonData: jsonData
            });
            
            if (result && result.success) {
                this.log('✅ 商品上传成功！');
                this.log('响应状态: ' + result.status);
                this.log('响应数据: ' + JSON.stringify(result.data, null, 2));
                
                alert('商品上传成功！');
            } else {
                this.log('❌ 商品上传失败: ' + (result?.error || '未知错误'), 'error');
                this.log('响应状态: ' + (result?.status || '未知'));
                if (result?.data) {
                    this.log('响应数据: ' + JSON.stringify(result.data, null, 2));
                }
                
                alert('商品上传失败: ' + (result?.error || '未知错误'));
            }
            
        } catch (error) {
            this.log('❌ 上传异常: ' + error.message, 'error');
            alert('上传异常: ' + error.message);
        }
    }

    // 日志记录
    log(message, level = 'info') {
        const logArea = document.getElementById('logArea');
        const timestamp = new Date().toLocaleTimeString();
        const levelIcon = level === 'error' ? '❌' : level === 'warn' ? '⚠️' : 'ℹ️';
        
        logArea.textContent += `[${timestamp}] ${levelIcon} ${message}\n`;
        logArea.scrollTop = logArea.scrollHeight;
        
        console.log(`[FinalUploadTester] ${message}`);
    }

    // 清除日志
    clearLog() {
        document.getElementById('logArea').textContent = '';
    }
}

// 全局实例
let finalUploadTester;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    finalUploadTester = new FinalUploadTester();
    
    // 绑定事件
    document.getElementById('checkSystemBtn').addEventListener('click', () => {
        finalUploadTester.checkSystemStatus();
    });
    
    document.getElementById('testJSZipBtn').addEventListener('click', () => {
        finalUploadTester.testBackgroundJSZip();
    });
    
    document.getElementById('loadSampleBtn').addEventListener('click', () => {
        finalUploadTester.loadSampleData();
    });
    
    document.getElementById('formatJsonBtn').addEventListener('click', () => {
        finalUploadTester.formatJson();
    });
    
    document.getElementById('compressJsonBtn').addEventListener('click', () => {
        finalUploadTester.compressJson();
    });
    
    document.getElementById('validateJsonBtn').addEventListener('click', () => {
        finalUploadTester.validateJson();
    });
    
    document.getElementById('uploadBtn').addEventListener('click', () => {
        finalUploadTester.uploadProduct();
    });
    
    document.getElementById('clearLogBtn').addEventListener('click', () => {
        finalUploadTester.clearLog();
    });
});
