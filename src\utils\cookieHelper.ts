// Cookie辅助工具
// 提供跨环境的Cookie获取和管理功能

// Cookie信息接口
export interface CookieInfo {
  name: string
  value: string
  domain?: string
  path?: string
  secure?: boolean
  httpOnly?: boolean
  sameSite?: string
  expires?: number
}

// Cookie辅助类
export class CookieHelper {
  // 获取所有Cookie（文档方式）
  public static getAllCookiesFromDocument(): Record<string, string> {
    const cookies: Record<string, string> = {}
    
    try {
      if (typeof document !== 'undefined' && document.cookie) {
        document.cookie.split(';').forEach(cookie => {
          const [name, value] = cookie.trim().split('=')
          if (name) {
            cookies[name] = decodeURIComponent(value || '')
          }
        })
      }
    } catch (error) {
      console.error('[CookieHelper] 从document获取Cookie失败:', error)
    }
    
    return cookies
  }
  
  // 获取店小秘相关的Cookie
  public static getDianxiaomiCookies(): Record<string, string> {
    const allCookies = this.getAllCookiesFromDocument()
    const dxmCookies: Record<string, string> = {}
    
    Object.keys(allCookies).forEach(key => {
      if (this.isDianxiaomiCookie(key)) {
        dxmCookies[key] = allCookies[key]
      }
    })
    
    return dxmCookies
  }
  
  // 判断是否为店小秘相关Cookie
  public static isDianxiaomiCookie(cookieName: string): boolean {
    const dxmPatterns = [
      'dxm_',
      'JSESSIONID',
      'MYJ_',
      'dianxiaomi',
      'DXM_',
      'session',
      'token',
      'auth'
    ]
    
    return dxmPatterns.some(pattern => 
      cookieName.toLowerCase().includes(pattern.toLowerCase())
    )
  }
  
  // 获取Cookie统计信息
  public static getCookieStats(): {
    totalCount: number
    dxmCount: number
    dxmCookieNames: string[]
    hasValidCookies: boolean
  } {
    const allCookies = this.getAllCookiesFromDocument()
    const dxmCookies = this.getDianxiaomiCookies()
    const dxmCookieNames = Object.keys(dxmCookies)
    
    return {
      totalCount: Object.keys(allCookies).length,
      dxmCount: dxmCookieNames.length,
      dxmCookieNames,
      hasValidCookies: dxmCookieNames.length > 0
    }
  }
  
  // 获取特定Cookie的值
  public static getCookie(name: string): string | null {
    try {
      if (typeof document !== 'undefined') {
        const value = `; ${document.cookie}`
        const parts = value.split(`; ${name}=`)
        if (parts.length === 2) {
          return decodeURIComponent(parts.pop()?.split(';').shift() || '')
        }
      }
    } catch (error) {
      console.error(`[CookieHelper] 获取Cookie ${name} 失败:`, error)
    }
    return null
  }
  
  // 检查是否在店小秘域名下
  public static isOnDianxiaomiDomain(): boolean {
    try {
      if (typeof window !== 'undefined' && window.location) {
        return window.location.hostname.includes('dianxiaomi.com')
      }
    } catch (error) {
      console.error('[CookieHelper] 检查域名失败:', error)
    }
    return false
  }
  
  // 获取详细的Cookie信息（用于调试）
  public static getDetailedCookieInfo(): {
    domain: string
    isOnDianxiaomiDomain: boolean
    cookieString: string
    cookieStats: ReturnType<typeof CookieHelper.getCookieStats>
    allCookies: Record<string, string>
    dxmCookies: Record<string, string>
  } {
    const domain = typeof window !== 'undefined' ? window.location.hostname : 'unknown'
    const isOnDianxiaomiDomain = this.isOnDianxiaomiDomain()
    const cookieString = typeof document !== 'undefined' ? document.cookie : ''
    const cookieStats = this.getCookieStats()
    const allCookies = this.getAllCookiesFromDocument()
    const dxmCookies = this.getDianxiaomiCookies()
    
    return {
      domain,
      isOnDianxiaomiDomain,
      cookieString,
      cookieStats,
      allCookies,
      dxmCookies
    }
  }
  
  // 验证Cookie是否有效（基于关键Cookie的存在）
  public static validateDianxiaomiCookies(): {
    isValid: boolean
    missingCookies: string[]
    presentCookies: string[]
    score: number
  } {
    const dxmCookies = this.getDianxiaomiCookies()
    const cookieNames = Object.keys(dxmCookies)
    
    // 关键Cookie列表（按重要性排序）
    const criticalCookies = ['JSESSIONID', 'dxm_token', 'MYJ_SESSION']
    const importantCookies = ['dxm_user', 'dxm_auth', 'session']
    
    const presentCookies: string[] = []
    const missingCookies: string[] = []
    
    // 检查关键Cookie
    criticalCookies.forEach(cookie => {
      const found = cookieNames.find(name => name.includes(cookie))
      if (found) {
        presentCookies.push(found)
      } else {
        missingCookies.push(cookie)
      }
    })
    
    // 检查重要Cookie
    importantCookies.forEach(cookie => {
      const found = cookieNames.find(name => name.includes(cookie))
      if (found && !presentCookies.includes(found)) {
        presentCookies.push(found)
      }
    })
    
    // 计算有效性分数
    const score = (presentCookies.length / (criticalCookies.length + importantCookies.length)) * 100
    const isValid = presentCookies.length > 0 && cookieNames.length > 0
    
    return {
      isValid,
      missingCookies,
      presentCookies,
      score: Math.round(score)
    }
  }
  
  // 格式化Cookie信息用于显示
  public static formatCookieForDisplay(cookies: Record<string, string>): string {
    return Object.entries(cookies)
      .map(([name, value]) => {
        const displayValue = value.length > 20 ? value.substring(0, 20) + '...' : value
        return `${name}: ${displayValue}`
      })
      .join('\n')
  }
  
  // 导出Cookie信息为JSON
  public static exportCookieInfo(): string {
    const info = this.getDetailedCookieInfo()
    const validation = this.validateDianxiaomiCookies()
    
    return JSON.stringify({
      ...info,
      validation,
      timestamp: Date.now(),
      userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : 'unknown'
    }, null, 2)
  }
}

// 便捷方法导出
export const getAllCookies = () => CookieHelper.getAllCookiesFromDocument()
export const getDianxiaomiCookies = () => CookieHelper.getDianxiaomiCookies()
export const getCookieStats = () => CookieHelper.getCookieStats()
export const validateCookies = () => CookieHelper.validateDianxiaomiCookies()
export const getCookieInfo = () => CookieHelper.getDetailedCookieInfo()
export const isOnDianxiaomiDomain = () => CookieHelper.isOnDianxiaomiDomain()

export default CookieHelper
