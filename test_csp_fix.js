// CSP修复测试脚本

class CSPTester {
    constructor() {
        this.init();
    }

    init() {
        this.log('CSP测试工具已初始化');
        this.checkCSPStatus();
    }

    checkCSPStatus() {
        const cspStatus = document.getElementById('cspStatus');
        const cspInfo = document.getElementById('cspInfo');
        
        try {
            // 检查是否可以执行外部脚本
            this.log('✅ 外部脚本加载成功');
            
            // 检查扩展环境
            if (typeof chrome !== 'undefined' && chrome.runtime) {
                this.log('✅ 扩展环境可用');
                cspStatus.className = 'status-card status-valid';
                cspInfo.innerHTML = `
                    <div style="color: #52c41a;">
                        ✅ CSP修复成功<br>
                        外部脚本可以正常加载<br>
                        扩展环境可用<br>
                        扩展ID: ${chrome.runtime.id}
                    </div>
                `;
            } else {
                this.log('⚠️ 扩展环境不可用（可能不在扩展环境中）');
                cspStatus.className = 'status-card status-valid';
                cspInfo.innerHTML = `
                    <div style="color: #52c41a;">
                        ✅ CSP修复成功<br>
                        外部脚本可以正常加载<br>
                        ⚠️ 扩展环境不可用（正常，如果不在扩展环境中）
                    </div>
                `;
            }
        } catch (error) {
            this.log('❌ CSP检查失败: ' + error.message, 'error');
            cspStatus.className = 'status-card status-invalid';
            cspInfo.innerHTML = `
                <div style="color: #ff4d4f;">
                    ❌ CSP检查失败<br>
                    错误: ${error.message}
                </div>
            `;
        }
    }

    testExtension() {
        try {
            if (typeof chrome !== 'undefined' && chrome.runtime) {
                this.log('✅ 扩展环境测试成功');
                this.log('扩展ID: ' + chrome.runtime.id);
                
                // 尝试发送消息测试
                chrome.runtime.sendMessage({ action: 'TEST_CONNECTION' }, (response) => {
                    if (chrome.runtime.lastError) {
                        this.log('⚠️ 扩展消息发送失败: ' + chrome.runtime.lastError.message, 'warn');
                    } else {
                        this.log('✅ 扩展消息发送成功');
                        if (response) {
                            this.log('响应: ' + JSON.stringify(response));
                        }
                    }
                });
            } else {
                this.log('❌ 扩展环境不可用', 'error');
            }
        } catch (error) {
            this.log('❌ 扩展测试失败: ' + error.message, 'error');
        }
    }

    testScript() {
        try {
            // 测试一些基本的JavaScript功能
            this.log('测试基本JavaScript功能...');
            
            // 测试DOM操作
            const testDiv = document.createElement('div');
            testDiv.textContent = '测试元素';
            this.log('✅ DOM操作正常');
            
            // 测试异步操作
            setTimeout(() => {
                this.log('✅ 异步操作正常');
            }, 100);
            
            // 测试Promise
            Promise.resolve('测试Promise').then(result => {
                this.log('✅ Promise操作正常: ' + result);
            });
            
            // 测试fetch（如果可用）
            if (typeof fetch !== 'undefined') {
                this.log('✅ Fetch API可用');
            } else {
                this.log('⚠️ Fetch API不可用', 'warn');
            }
            
            this.log('✅ 脚本功能测试完成');
        } catch (error) {
            this.log('❌ 脚本测试失败: ' + error.message, 'error');
        }
    }

    log(message, level = 'info') {
        const logArea = document.getElementById('logArea');
        const timestamp = new Date().toLocaleTimeString();
        const levelIcon = level === 'error' ? '❌' : level === 'warn' ? '⚠️' : 'ℹ️';
        
        logArea.textContent += `[${timestamp}] ${levelIcon} ${message}\n`;
        logArea.scrollTop = logArea.scrollHeight;
        
        console.log(`[CSPTester] ${message}`);
    }

    clearLog() {
        document.getElementById('logArea').textContent = '';
    }
}

// 全局实例
let cspTester;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    cspTester = new CSPTester();
    
    // 绑定事件
    document.getElementById('testExtensionBtn').addEventListener('click', () => {
        cspTester.testExtension();
    });
    
    document.getElementById('testScriptBtn').addEventListener('click', () => {
        cspTester.testScript();
    });
    
    document.getElementById('clearLogBtn').addEventListener('click', () => {
        cspTester.clearLog();
    });
});
