<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON>检测测试工具</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 8px;
            padding: 24px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .status-card {
            border: 1px solid #d9d9d9;
            border-radius: 6px;
            padding: 16px;
            margin: 16px 0;
        }
        .status-valid {
            border-color: #52c41a;
            background-color: #f6ffed;
        }
        .status-invalid {
            border-color: #ff4d4f;
            background-color: #fff2f0;
        }
        .status-warning {
            border-color: #faad14;
            background-color: #fffbe6;
        }
        .btn {
            background: #1890ff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 4px;
            font-size: 14px;
        }
        .btn:hover {
            background: #40a9ff;
        }
        .btn-success {
            background: #52c41a;
        }
        .btn-warning {
            background: #faad14;
        }
        .btn-danger {
            background: #ff4d4f;
        }
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 12px;
            margin: 16px 0;
        }
        .info-item {
            background: #fafafa;
            padding: 12px;
            border-radius: 4px;
            border-left: 3px solid #1890ff;
        }
        .info-label {
            font-weight: 600;
            color: #666;
            font-size: 12px;
            margin-bottom: 4px;
        }
        .info-value {
            font-size: 14px;
            color: #333;
        }
        .cookie-list {
            max-height: 300px;
            overflow-y: auto;
            background: #fafafa;
            padding: 12px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
        }
        .cookie-item {
            margin: 4px 0;
            padding: 4px 8px;
            background: white;
            border-radius: 2px;
            border-left: 3px solid #52c41a;
        }
        .cookie-item.dxm {
            border-left-color: #1890ff;
            background-color: #e6f7ff;
        }
        .log-area {
            background: #001529;
            color: #fff;
            padding: 16px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🍪 Cookie检测测试工具</h1>
        <p>此工具用于检测和分析当前页面的Cookie信息，特别是店小秘相关的Cookie</p>
        
        <div class="status-card" id="domainStatusCard">
            <h3>域名信息</h3>
            <div id="domainInfo">正在检测...</div>
        </div>
    </div>

    <div class="container">
        <h3>操作面板</h3>
        <button class="btn" id="detectCookiesBtn">🔍 检测Cookie</button>
        <button class="btn btn-success" id="validateCookiesBtn">✅ 验证Cookie</button>
        <button class="btn btn-warning" id="exportCookiesBtn">📤 导出Cookie信息</button>
        <button class="btn btn-danger" id="clearLogBtn">🗑️ 清除日志</button>
    </div>

    <div class="container">
        <h3>Cookie统计</h3>
        <div id="cookieStats" class="info-grid">
            <div class="info-item">
                <div class="info-label">总Cookie数</div>
                <div class="info-value" id="totalCookies">0</div>
            </div>
            <div class="info-item">
                <div class="info-label">店小秘Cookie数</div>
                <div class="info-value" id="dxmCookies">0</div>
            </div>
            <div class="info-item">
                <div class="info-label">验证分数</div>
                <div class="info-value" id="validationScore">0%</div>
            </div>
            <div class="info-item">
                <div class="info-label">Cookie状态</div>
                <div class="info-value" id="cookieStatus">未检测</div>
            </div>
        </div>
    </div>

    <div class="container">
        <h3>所有Cookie</h3>
        <div id="allCookiesList" class="cookie-list">点击"检测Cookie"按钮开始检测</div>
    </div>

    <div class="container">
        <h3>店小秘相关Cookie</h3>
        <div id="dxmCookiesList" class="cookie-list">点击"检测Cookie"按钮开始检测</div>
    </div>

    <div class="container">
        <h3>调试日志</h3>
        <div id="logArea" class="log-area"></div>
    </div>

    <script>
        // Cookie检测工具类
        class CookieDetector {
            constructor() {
                this.init();
            }

            init() {
                this.log('Cookie检测工具已初始化');
                this.updateDomainInfo();
                this.detectCookies();
            }

            updateDomainInfo() {
                const domainCard = document.getElementById('domainStatusCard');
                const domainInfo = document.getElementById('domainInfo');
                
                const hostname = window.location.hostname;
                const protocol = window.location.protocol;
                const port = window.location.port;
                const isSecure = protocol === 'https:';
                const isDianxiaomi = hostname.includes('dianxiaomi.com');
                
                domainCard.className = `status-card ${isDianxiaomi ? 'status-valid' : 'status-warning'}`;
                
                domainInfo.innerHTML = `
                    <div class="info-grid">
                        <div class="info-item">
                            <div class="info-label">域名</div>
                            <div class="info-value">${hostname}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">协议</div>
                            <div class="info-value">${protocol}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">端口</div>
                            <div class="info-value">${port || '默认'}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">是否安全</div>
                            <div class="info-value">${isSecure ? '✅ HTTPS' : '❌ HTTP'}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">店小秘域名</div>
                            <div class="info-value">${isDianxiaomi ? '✅ 是' : '❌ 否'}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">User Agent</div>
                            <div class="info-value" style="font-size: 10px;">${navigator.userAgent.substring(0, 50)}...</div>
                        </div>
                    </div>
                `;
            }

            detectCookies() {
                this.log('开始检测Cookie...');
                
                const allCookies = this.getAllCookies();
                const dxmCookies = this.getDianxiaomiCookies(allCookies);
                const validation = this.validateCookies(dxmCookies);
                
                this.updateStats(allCookies, dxmCookies, validation);
                this.updateCookieLists(allCookies, dxmCookies);
                
                this.log(`检测完成: 总Cookie ${Object.keys(allCookies).length} 个，店小秘Cookie ${Object.keys(dxmCookies).length} 个`);
            }

            getAllCookies() {
                const cookies = {};
                
                if (document.cookie) {
                    document.cookie.split(';').forEach(cookie => {
                        const [name, value] = cookie.trim().split('=');
                        if (name) {
                            cookies[name] = decodeURIComponent(value || '');
                        }
                    });
                }
                
                return cookies;
            }

            getDianxiaomiCookies(allCookies) {
                const dxmCookies = {};
                const patterns = ['dxm_', 'JSESSIONID', 'MYJ_', 'dianxiaomi', 'session', 'token', 'auth'];
                
                Object.keys(allCookies).forEach(key => {
                    if (patterns.some(pattern => key.toLowerCase().includes(pattern.toLowerCase()))) {
                        dxmCookies[key] = allCookies[key];
                    }
                });
                
                return dxmCookies;
            }

            validateCookies(dxmCookies) {
                const cookieNames = Object.keys(dxmCookies);
                const criticalCookies = ['JSESSIONID', 'dxm_token', 'MYJ_SESSION'];
                const importantCookies = ['dxm_user', 'dxm_auth', 'session'];
                
                const presentCookies = [];
                const missingCookies = [];
                
                criticalCookies.forEach(cookie => {
                    const found = cookieNames.find(name => name.includes(cookie));
                    if (found) {
                        presentCookies.push(found);
                    } else {
                        missingCookies.push(cookie);
                    }
                });
                
                importantCookies.forEach(cookie => {
                    const found = cookieNames.find(name => name.includes(cookie));
                    if (found && !presentCookies.includes(found)) {
                        presentCookies.push(found);
                    }
                });
                
                const score = (presentCookies.length / (criticalCookies.length + importantCookies.length)) * 100;
                const isValid = presentCookies.length > 0 && cookieNames.length > 0;
                
                return {
                    isValid,
                    missingCookies,
                    presentCookies,
                    score: Math.round(score)
                };
            }

            updateStats(allCookies, dxmCookies, validation) {
                document.getElementById('totalCookies').textContent = Object.keys(allCookies).length;
                document.getElementById('dxmCookies').textContent = Object.keys(dxmCookies).length;
                document.getElementById('validationScore').textContent = validation.score + '%';
                
                const statusElement = document.getElementById('cookieStatus');
                if (validation.isValid) {
                    statusElement.textContent = '✅ 有效';
                    statusElement.style.color = '#52c41a';
                } else {
                    statusElement.textContent = '❌ 无效';
                    statusElement.style.color = '#ff4d4f';
                }
            }

            updateCookieLists(allCookies, dxmCookies) {
                // 更新所有Cookie列表
                const allCookiesList = document.getElementById('allCookiesList');
                allCookiesList.innerHTML = Object.entries(allCookies)
                    .map(([name, value]) => {
                        const isDxm = Object.keys(dxmCookies).includes(name);
                        const displayValue = value.length > 50 ? value.substring(0, 50) + '...' : value;
                        return `<div class="cookie-item ${isDxm ? 'dxm' : ''}">
                            <strong>${name}:</strong> ${displayValue}
                        </div>`;
                    }).join('');
                
                // 更新店小秘Cookie列表
                const dxmCookiesList = document.getElementById('dxmCookiesList');
                if (Object.keys(dxmCookies).length > 0) {
                    dxmCookiesList.innerHTML = Object.entries(dxmCookies)
                        .map(([name, value]) => {
                            const displayValue = value.length > 50 ? value.substring(0, 50) + '...' : value;
                            return `<div class="cookie-item dxm">
                                <strong>${name}:</strong> ${displayValue}
                            </div>`;
                        }).join('');
                } else {
                    dxmCookiesList.innerHTML = '<div style="color: #ff4d4f; text-align: center; padding: 20px;">未发现店小秘相关Cookie</div>';
                }
            }

            exportCookieInfo() {
                const allCookies = this.getAllCookies();
                const dxmCookies = this.getDianxiaomiCookies(allCookies);
                const validation = this.validateCookies(dxmCookies);
                
                const exportData = {
                    timestamp: new Date().toISOString(),
                    domain: window.location.hostname,
                    protocol: window.location.protocol,
                    userAgent: navigator.userAgent,
                    cookieStats: {
                        totalCount: Object.keys(allCookies).length,
                        dxmCount: Object.keys(dxmCookies).length
                    },
                    validation,
                    allCookies,
                    dxmCookies
                };
                
                const dataStr = JSON.stringify(exportData, null, 2);
                const dataBlob = new Blob([dataStr], {type: 'application/json'});
                const url = URL.createObjectURL(dataBlob);
                
                const link = document.createElement('a');
                link.href = url;
                link.download = `cookie-info-${Date.now()}.json`;
                link.click();
                
                URL.revokeObjectURL(url);
                this.log('Cookie信息已导出');
            }

            log(message, level = 'info') {
                const logArea = document.getElementById('logArea');
                const timestamp = new Date().toLocaleTimeString();
                const levelIcon = level === 'error' ? '❌' : level === 'warn' ? '⚠️' : 'ℹ️';
                
                logArea.textContent += `[${timestamp}] ${levelIcon} ${message}\n`;
                logArea.scrollTop = logArea.scrollHeight;
                
                console.log(`[CookieDetector] ${message}`);
            }

            clearLog() {
                document.getElementById('logArea').textContent = '';
            }
        }

        // 全局实例
        let cookieDetector;

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', () => {
            cookieDetector = new CookieDetector();
            
            // 绑定事件
            document.getElementById('detectCookiesBtn').addEventListener('click', () => {
                cookieDetector.detectCookies();
            });
            
            document.getElementById('validateCookiesBtn').addEventListener('click', () => {
                const allCookies = cookieDetector.getAllCookies();
                const dxmCookies = cookieDetector.getDianxiaomiCookies(allCookies);
                const validation = cookieDetector.validateCookies(dxmCookies);
                
                cookieDetector.log(`验证结果: ${validation.isValid ? '有效' : '无效'}, 分数: ${validation.score}%`);
                cookieDetector.log(`存在的Cookie: ${validation.presentCookies.join(', ')}`);
                cookieDetector.log(`缺失的Cookie: ${validation.missingCookies.join(', ')}`);
            });
            
            document.getElementById('exportCookiesBtn').addEventListener('click', () => {
                cookieDetector.exportCookieInfo();
            });
            
            document.getElementById('clearLogBtn').addEventListener('click', () => {
                cookieDetector.clearLog();
            });
        });
    </script>
</body>
</html>
