# 店小秘数据上传测试页面使用说明

## 🚀 快速开始

### 📁 文件位置
测试页面文件：`test_upload.html`

### 🌐 打开方式

#### 方式一：直接打开（推荐）
1. 找到项目根目录下的 `test_upload.html` 文件
2. 双击文件，或右键选择"打开方式" → "浏览器"
3. 页面将在默认浏览器中打开

#### 方式二：浏览器地址栏
1. 打开任意浏览器
2. 在地址栏输入：`file:///完整路径/test_upload.html`
3. 例如：`file:///C:/Users/<USER>/项目目录/test_upload.html`

#### 方式三：拖拽打开
1. 打开浏览器
2. 将 `test_upload.html` 文件直接拖拽到浏览器窗口中

## 🔧 功能说明

### 📦 核心处理流程
```
JSON数据 → choiceSave.txt → ZIP压缩 → 上传到店小秘API
```

### 🎯 主要功能

#### 1. JSON数据处理
- **输入区域**：大文本框支持输入/粘贴JSON数据
- **格式化**：美化JSON显示，便于阅读
- **压缩**：将JSON压缩为单行格式
- **验证**：检查JSON格式是否正确

#### 2. 示例数据
- **一键加载**：点击"加载示例数据"按钮
- **基于真实数据**：使用 `docs/dxm/choiceSave.txt` 中的实际数据

#### 3. ZIP文件处理
- **自动创建**：将JSON内容写入choiceSave.txt，然后打包成ZIP
- **测试功能**：可以单独测试ZIP文件创建过程
- **压缩信息**：显示原始大小、压缩后大小、压缩率

#### 4. 上传功能
- **目标API**：`https://www.dianxiaomi.com/api/popTemuProduct/add.json`
- **请求格式**：`multipart/form-data`
- **文件参数**：`name="file"; filename="blob"`
- **其他参数**：`op=1`

#### 5. 认证处理
- **自动获取**：自动读取浏览器中店小秘网站的Cookie
- **实时检测**：页面顶部显示认证状态
- **支持类型**：JSESSIONID、dxm_*、MYJ_* 等相关Cookie

#### 6. 响应显示
- **详细信息**：显示完整的HTTP响应
- **格式化**：JSON响应自动美化显示
- **复制功能**：一键复制响应内容
- **错误处理**：显示详细的错误信息

### 🔍 调试功能

#### 调试面板
- **Cookie查看**：显示所有相关认证Cookie
- **CORS测试**：测试跨域请求是否正常
- **下载测试**：下载ZIP或TXT格式的测试文件
- **详细日志**：实时显示操作日志

## 📋 使用步骤

### 第一步：准备环境
1. 确保网络连接正常（需要加载JSZip库）
2. 在浏览器中登录店小秘网站（获取认证信息）
3. 打开测试页面

### 第二步：输入数据
1. 在文本框中输入JSON数据，或
2. 点击"加载示例数据"使用预设数据
3. 可选：点击"格式化JSON"美化显示

### 第三步：测试上传
1. 确认页面顶部显示"✅ 已检测到店小秘认证信息"
2. 点击"🚀 测试上传"按钮
3. 等待处理完成

### 第四步：查看结果
1. 查看状态提示（成功/失败）
2. 在响应区域查看详细的API返回信息
3. 如需要，点击"复制"按钮复制响应内容

## ⚠️ 注意事项

### 认证要求
- **必须先登录**：需要在同一浏览器中登录店小秘网站
- **Cookie有效期**：如果长时间未操作，可能需要重新登录
- **域名限制**：建议在店小秘相关域名下使用

### 文件要求
- **JSZip库**：确保 `src/lib/jszip.min.js` 文件存在（已包含在项目中）
- **API访问**：需要能访问店小秘API服务器
- **CORS处理**：如遇跨域问题，建议使用浏览器扩展或在店小秘域名下使用

### 数据格式
- **JSON格式**：输入数据必须是有效的JSON格式
- **文件大小**：建议单个文件不超过10MB
- **字符编码**：使用UTF-8编码

## 🔧 故障排除

### 常见问题

#### 1. "未检测到店小秘认证信息"
**解决方案**：
- 在同一浏览器中打开店小秘网站并登录
- 刷新测试页面
- 检查浏览器是否禁用了Cookie

#### 2. "JSZip库未加载"
**解决方案**：
- 检查 `src/lib/jszip.min.js` 文件是否存在
- 确保文件路径正确
- 刷新页面重新加载

#### 3. "网络错误: 无法连接到服务器"
**解决方案**：
- 检查网络连接
- 确认店小秘服务器状态
- 尝试在店小秘域名下使用测试页面

#### 4. CORS跨域错误
**解决方案**：
- 使用浏览器扩展绕过CORS限制
- 将测试页面部署到店小秘网站
- 使用支持CORS的浏览器设置

### 调试技巧
1. **打开调试面板**：点击"🔧 调试面板"查看详细信息
2. **查看浏览器控制台**：按F12打开开发者工具查看详细日志
3. **测试ZIP创建**：单独测试ZIP文件创建是否正常
4. **下载测试文件**：下载生成的文件进行本地验证

## 📞 技术支持

如遇到问题，请提供以下信息：
- 浏览器类型和版本
- 错误信息截图
- 浏览器控制台日志
- 网络环境描述

---

**版本**：v1.0  
**更新时间**：2025-01-18  
**兼容性**：Chrome 80+, Firefox 75+, Safari 13+, Edge 80+
