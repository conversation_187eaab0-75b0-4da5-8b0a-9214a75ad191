<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { DownOutlined } from '@ant-design/icons-vue'
import type { ProductCategory } from '../../../services/dianxiaomiDetectionService'

// Props
interface Props {
  value?: number[]
  placeholder?: string
  shopId?: string
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: '请选择商品分类',
  shopId: ''
})

// Emits
const emit = defineEmits<{
  'update:value': [value: number[]]
  'change': [value: number[], selectedOptions: CascaderOption[]]
}>()

// 级联选择器选项类型
interface CascaderOption {
  value: number
  label: string
  isLeaf?: boolean
  loading?: boolean
  children?: CascaderOption[]
}

// 状态
const loading = ref(false)
const options = ref<CascaderOption[]>([])

// 计算属性
const selectedValue = computed({
  get: () => props.value || [],
  set: (value) => emit('update:value', value)
})

// 加载分类数据 - 直接调用店小秘API
const loadCategories = async (parentId?: number): Promise<ProductCategory[]> => {
  if (!props.shopId) {
    console.warn('[CategoryCascader] 缺少 shopId 参数')
    return []
  }

  try {
    console.info('[CategoryCascader] 直接调用店小秘API加载分类数据，父ID:', parentId, '店铺ID:', props.shopId)

    // 构建POST请求参数
    const formData = new URLSearchParams()
    formData.append('shopId', props.shopId)
    formData.append('categoryParentId', (parentId || 0).toString())

    console.info('[CategoryCascader] 请求参数:', {
      shopId: props.shopId,
      categoryParentId: parentId || 0
    })

    // 直接调用店小秘API
    const response = await fetch('https://www.dianxiaomi.com/api/popTemuCategory/list.json', {
      method: 'POST',
      credentials: 'include',
      headers: {
        'Accept': '*/*',
        'Content-Type': 'application/x-www-form-urlencoded',
        'X-Requested-With': 'XMLHttpRequest'
      },
      body: formData.toString()
    })

    console.info('[CategoryCascader] API响应状态:', response.status)

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    const data = await response.json()
    console.info('[CategoryCascader] API响应数据:', data)

    if (data.code === 0) {
      console.info('[CategoryCascader] 分类数据加载成功，数量:', data.data?.length || 0)
      return data.data || []
    } else {
      throw new Error(data.msg || '获取分类失败')
    }
  } catch (error) {
    console.error('[CategoryCascader] 加载分类失败:', error)
    throw error
  }
}

// 转换分类数据为级联选择器格式
const transformCategories = (categories: ProductCategory[]): CascaderOption[] => {
  return categories.map(category => ({
    value: category.catId,
    label: category.catName,
    isLeaf: category.isLeaf,
    loading: false,
    children: category.isLeaf ? undefined : []
  }))
}

// 初始化加载根分类
const initializeCategories = async () => {
  if (!props.shopId) {
    console.warn('[CategoryCascader] shopId 为空，无法加载分类')
    return
  }

  console.info('[CategoryCascader] 开始初始化分类，shopId:', props.shopId)
  loading.value = true
  try {
    const categories = await loadCategories()
    console.info('[CategoryCascader] 获取到根分类数据:', categories)
    options.value = transformCategories(categories)
    console.info('[CategoryCascader] 转换后的选项:', options.value)
  } catch (error) {
    console.error('[CategoryCascader] 初始化分类失败:', error)
  } finally {
    loading.value = false
  }
}

// 动态加载子分类
const loadData = async (selectedOptions: CascaderOption[]) => {
  const targetOption = selectedOptions[selectedOptions.length - 1]
  
  if (targetOption.isLeaf) {
    return
  }

  targetOption.loading = true

  try {
    const categories = await loadCategories(targetOption.value)
    targetOption.children = transformCategories(categories)
  } catch (error) {
    console.error('[CategoryCascader] 加载子分类失败:', error)
  } finally {
    targetOption.loading = false
  }
}

// 选择变化处理
const handleChange = (value: number[], selectedOptions: CascaderOption[]) => {
  console.info('[CategoryCascader] 分类选择变化:', { value, selectedOptions })

  // 确保 selectedOptions 有正确的数据结构
  if (selectedOptions && selectedOptions.length > 0) {
    console.info('[CategoryCascader] 触发 change 事件:', {
      value,
      selectedOptions: selectedOptions.map(opt => ({
        value: opt.value,
        label: opt.label,
        isLeaf: opt.isLeaf
      }))
    })
    emit('change', value, selectedOptions)
  } else {
    console.warn('[CategoryCascader] selectedOptions 为空或无效:', selectedOptions)
    emit('change', value, [])
  }
}

// 监听 shopId 变化
watch(() => props.shopId, (newShopId) => {
  if (newShopId) {
    initializeCategories()
  }
}, { immediate: true })

// 显示路径
const displayRender = ({ labels }: { labels: string[] }) => {
  return labels.join(' / ')
}

// 过滤选项
const filter = (inputValue: string, path: CascaderOption[]) => {
  return path.some(option => option.label.toLowerCase().includes(inputValue.toLowerCase()))
}
</script>

<template>
  <a-cascader
    v-model:value="selectedValue"
    :options="options"
    :load-data="loadData"
    :placeholder="placeholder"
    :loading="loading"
    :display-render="displayRender"
    :filter="filter"
    :show-search="true"
    change-on-select
    expand-trigger="hover"
    @change="handleChange"
    class="w-full"
  >
    <template #suffixIcon>
      <DownOutlined />
    </template>
    
    <template #notFoundContent>
      <div class="text-center py-4 text-gray-500">
        <div v-if="!shopId">请先选择店铺账号</div>
        <div v-else-if="loading">加载中...</div>
        <div v-else>暂无分类数据</div>
      </div>
    </template>
  </a-cascader>
</template>



<style scoped>
:deep(.ant-cascader-picker) {
  width: 100%;
}

:deep(.ant-cascader-menu) {
  max-height: 300px;
}

:deep(.ant-cascader-menu-item-loading-icon) {
  margin-right: 8px;
}
</style>
