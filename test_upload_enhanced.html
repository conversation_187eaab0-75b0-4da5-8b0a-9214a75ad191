<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>增强版商品上传测试工具</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 8px;
            padding: 24px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .status-card {
            border: 1px solid #d9d9d9;
            border-radius: 6px;
            padding: 16px;
            margin: 16px 0;
        }
        .status-valid {
            border-color: #52c41a;
            background-color: #f6ffed;
        }
        .status-invalid {
            border-color: #ff4d4f;
            background-color: #fff2f0;
        }
        .status-warning {
            border-color: #faad14;
            background-color: #fffbe6;
        }
        .btn {
            background: #1890ff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 4px;
            font-size: 14px;
        }
        .btn:hover {
            background: #40a9ff;
        }
        .btn:disabled {
            background: #d9d9d9;
            cursor: not-allowed;
        }
        .btn-success {
            background: #52c41a;
        }
        .btn-warning {
            background: #faad14;
        }
        .btn-danger {
            background: #ff4d4f;
        }
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 12px;
            margin: 16px 0;
        }
        .info-item {
            background: #fafafa;
            padding: 12px;
            border-radius: 4px;
            border-left: 3px solid #1890ff;
        }
        .info-label {
            font-weight: 600;
            color: #666;
            font-size: 12px;
            margin-bottom: 4px;
        }
        .info-value {
            font-size: 14px;
            color: #333;
        }
        .form-group {
            margin: 16px 0;
        }
        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }
        .form-input, .form-select, .form-textarea {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            font-size: 14px;
        }
        .form-textarea {
            min-height: 80px;
            resize: vertical;
        }
        .log-area {
            background: #001529;
            color: #fff;
            padding: 16px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        .loading {
            opacity: 0.6;
            pointer-events: none;
        }
        .extension-warning {
            background: #fff7e6;
            border: 1px solid #ffd591;
            border-radius: 6px;
            padding: 16px;
            margin: 16px 0;
            color: #d46b08;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 增强版商品上传测试工具</h1>
        <p>此工具通过浏览器扩展的background.js调用店小秘API，解决跨域问题</p>
        
        <div id="extensionStatus" class="extension-warning">
            <h4>⚠️ 扩展状态检查</h4>
            <div id="extensionInfo">正在检查扩展状态...</div>
        </div>
    </div>

    <div class="container">
        <h3>连接状态</h3>
        <div class="status-card" id="connectionStatusCard">
            <div id="connectionStatus">正在检查连接状态...</div>
        </div>
        
        <div class="info-grid" id="connectionInfo">
            <div class="info-item">
                <div class="info-label">扩展连接</div>
                <div class="info-value" id="extensionConnection">检查中...</div>
            </div>
            <div class="info-item">
                <div class="info-label">店小秘标签页</div>
                <div class="info-value" id="dianxiaomiTab">检查中...</div>
            </div>
            <div class="info-item">
                <div class="info-label">Token状态</div>
                <div class="info-value" id="tokenStatus">检查中...</div>
            </div>
            <div class="info-item">
                <div class="info-label">用户信息</div>
                <div class="info-value" id="userInfo">检查中...</div>
            </div>
        </div>
    </div>

    <div class="container">
        <h3>操作面板</h3>
        <button class="btn" id="checkConnectionBtn">🔍 检查连接状态</button>
        <button class="btn btn-success" id="getTokenStatusBtn">🔑 获取Token状态</button>
        <button class="btn btn-success" id="getShopListBtn">🏪 获取店铺列表</button>
        <button class="btn btn-warning" id="testAPIBtn">🧪 测试API调用</button>
        <button class="btn btn-danger" id="clearLogBtn">🗑️ 清除日志</button>
    </div>

    <div class="container">
        <h3>店铺信息</h3>
        <div id="shopInfo" class="info-grid">
            <div class="info-item">
                <div class="info-label">说明</div>
                <div class="info-value">点击"获取店铺列表"按钮获取店铺信息</div>
            </div>
        </div>
    </div>

    <div class="container">
        <h3>🚀 店小秘商品上传 - 借鉴test_upload.html逻辑</h3>

        <div class="status-card status-warning">
            <h4>📋 上传说明</h4>
            <ul style="margin: 8px 0; padding-left: 20px;">
                <li><strong>目标API：</strong> https://www.dianxiaomi.com/api/popTemuProduct/add.json</li>
                <li><strong>处理流程：</strong> JSON → choiceSave.txt → ZIP压缩 → 上传</li>
                <li><strong>文件格式：</strong> form-data; name="file"; filename="blob"</li>
                <li><strong>参数：</strong> op=1, file=ZIP附件</li>
                <li><strong>认证：</strong> 通过扩展background.js获取店小秘Cookie和Token</li>
            </ul>
        </div>

        <div class="form-group">
            <label class="form-label" for="jsonData">JSON数据内容：</label>
            <div style="display: flex; gap: 10px; margin-bottom: 10px;">
                <button type="button" class="btn" id="formatJsonBtn">格式化JSON</button>
                <button type="button" class="btn" id="compressJsonBtn">压缩JSON</button>
                <button type="button" class="btn btn-warning" id="loadSampleBtn">加载示例数据</button>
                <button type="button" class="btn" id="validateJsonBtn">验证JSON</button>
            </div>
            <textarea class="form-textarea" id="jsonData" placeholder="请在此输入JSON数据..." style="height: 200px; font-family: 'Courier New', monospace; font-size: 12px;"></textarea>
        </div>

        <div style="text-align: center; margin: 20px 0;">
            <button type="button" class="btn btn-success" id="uploadDataBtn" style="padding: 12px 30px; font-size: 16px;">🚀 测试上传</button>
        </div>
    </div>

    <div class="container">
        <h3>调试日志</h3>
        <div id="logArea" class="log-area"></div>
    </div>

    <script src="test_upload_enhanced.js"></script>
</body>
</html>
