import amazonDataService from '../services/amazonDataService'
import configStorageService from '../services/configStorageService'

// 采集状态管理
let isCollecting = false
let collectedCount = 0

// 创建批量采集按钮（右上角）
function createBatchCollectionButton(): HTMLElement {
  const button = document.createElement('button')
  button.className = 'amazon-batch-collect-btn'
  button.type = 'button'

  // 获取匹配的商品数量
  const productCount = getMatchedProductCount()

  button.style.cssText = `
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9999;
    padding: 8px 16px;
    border-radius: 6px;
    border: none;
    background: #ff4d4f;
    color: white;
    cursor: pointer;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
  `

  // 创建图标
  const icon = document.createElement('div')
  icon.innerHTML = `
    <svg width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
      <path d="M8 0a8 8 0 1 1 0 16A8 8 0 0 1 8 0zM4.5 7.5a.5.5 0 0 0 0 1h5.793l-2.147 2.146a.5.5 0 0 0 .708.708l3-3a.5.5 0 0 0 0-.708l-3-3a.5.5 0 1 0-.708.708L10.293 7.5H4.5z"/>
    </svg>
  `
  button.appendChild(icon)

  // 创建文字和数量
  const textContainer = document.createElement('div')
  textContainer.style.cssText = `
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    line-height: 1.2;
  `

  const mainText = document.createElement('span')
  mainText.textContent = '批量采集'
  mainText.style.fontSize = '14px'

  const countText = document.createElement('span')
  countText.className = 'product-count'
  countText.textContent = `${productCount} 个商品`
  countText.style.cssText = `
    font-size: 12px;
    opacity: 0.9;
    font-weight: normal;
  `

  textContainer.appendChild(mainText)
  textContainer.appendChild(countText)
  button.appendChild(textContainer)

  // 添加点击事件
  button.addEventListener('click', handleCollectionClick)

  // 添加悬停效果
  button.addEventListener('mouseenter', () => {
    button.style.transform = 'scale(1.05)'
    button.style.boxShadow = '0 4px 12px rgba(255, 77, 79, 0.3)'
  })

  button.addEventListener('mouseleave', () => {
    button.style.transform = 'scale(1)'
    button.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.15)'
  })

  return button
}

// 获取匹配的商品数量
function getMatchedProductCount(): number {
  const productElements = document.querySelectorAll('[data-asin]:not(.amazon-collected)')
  return productElements.length
}

// 创建单品采集按钮（悬浮在商品图片上）
function createProductCollectionButton(productElement: Element): HTMLElement {
  const button = document.createElement('div')
  button.className = 'amazon-product-collect-btn'
  button.style.cssText = `
    position: absolute;
    top: 8px;
    right: 8px;
    background: rgba(255, 77, 79, 0.9);
    color: white;
    padding: 6px 12px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    user-select: none;
    z-index: 10;
    opacity: 0;
    transform: translateY(-5px);
    backdrop-filter: blur(4px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  `
  button.textContent = '单品采集'

  // 添加悬停效果
  button.addEventListener('mouseenter', () => {
    button.style.background = 'rgba(255, 77, 79, 1)'
    button.style.transform = 'translateY(-5px) scale(1.05)'
  })

  button.addEventListener('mouseleave', () => {
    button.style.background = 'rgba(255, 77, 79, 0.9)'
    button.style.transform = 'translateY(-5px) scale(1)'
  })

  // 添加点击事件
  button.addEventListener('click', (e) => {
    e.preventDefault()
    e.stopPropagation()
    handleSingleProductCollection(productElement)
  })

  return button
}

// 处理采集按钮点击
async function handleCollectionClick() {
  if (isCollecting) {
    showNotification('正在采集中，请稍候...', 'warning')
    return
  }

  try {
    // 检查配置完整性
    const configCheck = await configStorageService.isConfigComplete()
    if (!configCheck.isComplete) {
      showNotification(`配置不完整，缺少：${configCheck.missingFields.join(', ')}`, 'error')
      return
    }

    if (isAmazonSearchPage()) {
      await handleBatchCollection()
    } else if (isAmazonProductPage()) {
      await handleSingleProductCollection()
    } else {
      showNotification('当前页面不支持采集', 'warning')
    }
  } catch (error) {
    console.error('[AmazonCollector] 采集失败:', error)
    showNotification('采集失败: ' + (error instanceof Error ? error.message : '未知错误'), 'error')
  }
}

// 处理单品采集
async function handleSingleProductCollection(productElement?: Element) {
  try {
    isCollecting = true
    updateButtonState()
    
    showNotification('开始采集商品数据...', 'info')

    // 提取Amazon产品数据
    const amazonData = await amazonDataService.extractProductDataFromPage()
    if (!amazonData) {
      throw new Error('无法提取产品数据')
    }

    // 转换为店小秘格式
    const dianxiaomiData = await amazonDataService.convertToDianxiaomiFormat(amazonData)

    // 模拟推送到店小秘
    await amazonDataService.simulatePushToDianxiaomi(dianxiaomiData)

    collectedCount++
    updateProductCount()
    
    showNotification(`商品 "${amazonData.title.substring(0, 30)}..." 采集成功！`, 'success')

    // 如果是从产品列表中采集，标记该产品已采集
    if (productElement) {
      markProductAsCollected(productElement)
    }

  } catch (error) {
    console.error('[AmazonCollector] 单品采集失败:', error)
    showNotification('单品采集失败: ' + (error instanceof Error ? error.message : '未知错误'), 'error')
  } finally {
    isCollecting = false
    updateButtonState()
  }
}

// 处理批量采集
async function handleBatchCollection() {
  try {
    isCollecting = true
    updateButtonState()

    showNotification('开始批量采集...', 'info')

    // 获取当前页面的所有产品
    const productElements = document.querySelectorAll('[data-asin]:not(.amazon-collected)')
    
    if (productElements.length === 0) {
      showNotification('当前页面没有找到可采集的商品', 'warning')
      return
    }

    let successCount = 0
    let failCount = 0

    for (let i = 0; i < productElements.length; i++) {
      const productElement = productElements[i]
      const asin = productElement.getAttribute('data-asin')
      
      if (!asin) continue

      try {
        showNotification(`正在采集第 ${i + 1}/${productElements.length} 个商品...`, 'info')
        
        // 模拟点击产品链接获取详情
        const productLink = productElement.querySelector('h3 a, .s-link-style a') as HTMLAnchorElement
        if (productLink) {
          // 这里可以实现在新标签页中打开产品页面进行采集
          // 暂时跳过，直接标记为已采集
          markProductAsCollected(productElement)
          successCount++
          collectedCount++
        }

        // 添加延迟避免请求过快
        await new Promise(resolve => setTimeout(resolve, 1000))

      } catch (error) {
        console.error(`[AmazonCollector] 采集商品 ${asin} 失败:`, error)
        failCount++
      }
    }

    updateProductCount()
    showNotification(`批量采集完成！成功: ${successCount}, 失败: ${failCount}`, 'success')

    // 检查是否有下一页
    if (hasNextPage()) {
      const shouldContinue = confirm('当前页面采集完成，是否继续采集下一页？')
      if (shouldContinue) {
        goToNextPage()
      }
    }

  } catch (error) {
    console.error('[AmazonCollector] 批量采集失败:', error)
    showNotification('批量采集失败: ' + (error instanceof Error ? error.message : '未知错误'), 'error')
  } finally {
    isCollecting = false
    updateButtonState()
  }
}

// 标记产品为已采集
function markProductAsCollected(productElement: Element) {
  productElement.classList.add('amazon-collected')
  productElement.setAttribute('style', 'opacity: 0.6; border: 2px solid #52c41a;')
  
  // 添加已采集标记
  const badge = document.createElement('div')
  badge.style.cssText = `
    position: absolute;
    top: 8px;
    right: 8px;
    background: #52c41a;
    color: white;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 10px;
    z-index: 10;
  `
  badge.textContent = '已采集'
  
  const container = productElement.querySelector('[data-cy="title-recipe-container"], .s-result-item')
  if (container) {
    container.style.position = 'relative'
    container.appendChild(badge)
  }
}

// 更新按钮状态
function updateButtonState() {
  const button = document.querySelector('.amazon-batch-collect-btn') as HTMLElement
  if (button) {
    if (isCollecting) {
      button.style.background = '#faad14'
      button.style.cursor = 'not-allowed'
      const mainText = button.querySelector('span:first-child') as HTMLElement
      if (mainText) {
        mainText.textContent = '采集中...'
      }
    } else {
      button.style.background = '#ff4d4f'
      button.style.cursor = 'pointer'
      const mainText = button.querySelector('span:first-child') as HTMLElement
      if (mainText) {
        mainText.textContent = '批量采集'
      }
    }
  }
}

// 更新商品数量显示
function updateProductCount() {
  const countText = document.querySelector('.product-count') as HTMLElement
  if (countText) {
    const productCount = getMatchedProductCount()
    countText.textContent = `${productCount} 个商品`
  }
}

// 显示通知
function showNotification(message: string, type: 'success' | 'error' | 'warning' | 'info' = 'info') {
  // 创建通知元素
  const notification = document.createElement('div')
  notification.style.cssText = `
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 10000;
    padding: 12px 16px;
    border-radius: 6px;
    color: white;
    font-size: 14px;
    max-width: 300px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transition: all 0.3s ease;
    transform: translateX(100%);
  `

  // 设置颜色
  const colors = {
    success: '#52c41a',
    error: '#ff4d4f',
    warning: '#faad14',
    info: '#1677ff'
  }
  notification.style.background = colors[type]
  notification.textContent = message

  document.body.appendChild(notification)

  // 动画显示
  setTimeout(() => {
    notification.style.transform = 'translateX(0)'
  }, 100)

  // 自动隐藏
  setTimeout(() => {
    notification.style.transform = 'translateX(100%)'
    setTimeout(() => {
      document.body.removeChild(notification)
    }, 300)
  }, 3000)
}

// 工具函数
function isAmazonSearchPage(): boolean {
  return window.location.href.includes('amazon.com/s') || 
         window.location.href.includes('amazon.com/gp/search')
}

function isAmazonProductPage(): boolean {
  return window.location.href.includes('amazon.com') && 
         (window.location.href.includes('/dp/') || window.location.href.includes('/gp/product/'))
}

function hasNextPage(): boolean {
  const nextPageLink = document.querySelector('a[aria-label*="next page"], a[aria-label*="Next page"]')
  return nextPageLink !== null
}

function goToNextPage() {
  const nextPageLink = document.querySelector('a[aria-label*="next page"], a[aria-label*="Next page"]') as HTMLAnchorElement
  if (nextPageLink) {
    window.location.href = nextPageLink.href
  }
}

// 初始化
function init() {
  // 检查是否为Amazon页面
  if (!window.location.href.includes('amazon.com')) {
    return
  }

  console.info('[AmazonCollector] 初始化Amazon采集器...')

  // 等待页面加载完成
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', init)
    return
  }

  // 如果是搜索页面，创建批量采集按钮和单品采集按钮
  if (isAmazonSearchPage()) {
    const batchButton = createBatchCollectionButton()
    document.body.appendChild(batchButton)
    addProductCollectionButtons()
  }

  console.info('[AmazonCollector] Amazon采集器初始化完成')
}

// 为产品添加采集按钮
function addProductCollectionButtons() {
  const productElements = document.querySelectorAll('[data-asin]')

  productElements.forEach(productElement => {
    // 避免重复添加
    if (productElement.querySelector('.amazon-product-collect-btn')) {
      return
    }

    // 查找商品图片容器
    const imageContainer = productElement.querySelector('.s-product-image-container, .s-image-container')
    if (imageContainer) {
      // 设置图片容器为相对定位
      const container = imageContainer as HTMLElement
      container.style.position = 'relative'

      const collectButton = createProductCollectionButton(productElement)
      container.appendChild(collectButton)

      // 添加悬停效果显示/隐藏按钮
      container.addEventListener('mouseenter', () => {
        collectButton.style.opacity = '1'
        collectButton.style.transform = 'translateY(0)'
      })

      container.addEventListener('mouseleave', () => {
        collectButton.style.opacity = '0'
        collectButton.style.transform = 'translateY(-5px)'
      })
    }
  })

  // 更新商品数量显示
  updateProductCount()
}

// 启动
init()

// 监听页面变化（SPA路由）
let lastUrl = location.href
new MutationObserver(() => {
  const url = location.href
  if (url !== lastUrl) {
    lastUrl = url
    setTimeout(init, 1000) // 延迟重新初始化
  }
}).observe(document, { subtree: true, childList: true })
