<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>店小秘Token检测测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 8px;
            padding: 24px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .status-card {
            border: 1px solid #d9d9d9;
            border-radius: 6px;
            padding: 16px;
            margin: 16px 0;
        }
        .status-valid {
            border-color: #52c41a;
            background-color: #f6ffed;
        }
        .status-invalid {
            border-color: #ff4d4f;
            background-color: #fff2f0;
        }
        .status-warning {
            border-color: #faad14;
            background-color: #fffbe6;
        }
        .btn {
            background: #1890ff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 4px;
            font-size: 14px;
        }
        .btn:hover {
            background: #40a9ff;
        }
        .btn:disabled {
            background: #d9d9d9;
            cursor: not-allowed;
        }
        .btn-success {
            background: #52c41a;
        }
        .btn-warning {
            background: #faad14;
        }
        .btn-danger {
            background: #ff4d4f;
        }
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 12px;
            margin: 16px 0;
        }
        .info-item {
            background: #fafafa;
            padding: 12px;
            border-radius: 4px;
            border-left: 3px solid #1890ff;
        }
        .info-label {
            font-weight: 600;
            color: #666;
            font-size: 12px;
            margin-bottom: 4px;
        }
        .info-value {
            font-size: 14px;
            color: #333;
        }
        .log-area {
            background: #001529;
            color: #fff;
            padding: 16px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        .cookie-list {
            max-height: 200px;
            overflow-y: auto;
            background: #fafafa;
            padding: 12px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
        }
        .cookie-item {
            margin: 4px 0;
            padding: 4px 8px;
            background: white;
            border-radius: 2px;
            border-left: 3px solid #52c41a;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 店小秘Token检测测试工具</h1>
        <p>此工具用于测试新的店小秘Token检测服务功能</p>

        <div class="status-card status-warning">
            <h4>📋 使用说明</h4>
            <ol style="margin: 8px 0; padding-left: 20px;">
                <li>首先在店小秘网站登录</li>
                <li>打开扩展的BasicSettings页面</li>
                <li>点击"🔄 刷新"按钮检测Token状态</li>
                <li>或点击"💾 保存基础设置"按钮保存Token</li>
                <li>然后回到此页面测试Token检测功能</li>
            </ol>
        </div>
        
        <div class="status-card" id="tokenStatusCard">
            <h3>Token状态</h3>
            <div id="tokenStatus">正在初始化...</div>
            <div class="info-grid" id="tokenInfo" style="display: none;"></div>
        </div>
        
        <div class="container">
            <h3>操作面板</h3>
            <button class="btn" id="checkTokenBtn">🔍 检查Token状态</button>
            <button class="btn" id="refreshTokenBtn">🔄 强制刷新Token</button>
            <button class="btn btn-warning" id="showCookieBtn">🍪 查看Cookie详情</button>
            <button class="btn btn-warning" id="showCacheBtn">📊 查看缓存信息</button>
            <button class="btn btn-danger" id="clearCacheBtn">🗑️ 清除缓存</button>
            <button class="btn" id="testAPIBtn">🧪 测试API调用</button>
        </div>
        
        <div class="container">
            <h3>Cookie信息</h3>
            <div id="cookieInfo" class="cookie-list">点击"查看Cookie详情"按钮查看</div>
        </div>
        
        <div class="container">
            <h3>缓存信息</h3>
            <div id="cacheInfo" class="info-grid">点击"查看缓存信息"按钮查看</div>
        </div>

        <div class="container">
            <h3>跨页面缓存</h3>
            <div id="crossPageCacheInfo" class="info-grid">
                <div class="info-item">
                    <div class="info-label">说明</div>
                    <div class="info-value">此缓存可在不同页面间共享token状态</div>
                </div>
            </div>
        </div>

        <div class="container">
            <h3>跨页面缓存</h3>
            <div id="crossPageCacheInfo" class="info-grid">
                <div class="info-item">
                    <div class="info-label">说明</div>
                    <div class="info-value">此缓存可在不同页面间共享token状态</div>
                </div>
            </div>
        </div>
        
        <div class="container">
            <h3>调试日志</h3>
            <div id="logArea" class="log-area"></div>
            <button class="btn btn-warning" id="clearLogBtn">清除日志</button>
        </div>
    </div>

    <script src="test_token_detection.js"></script>
</body>
</html>

