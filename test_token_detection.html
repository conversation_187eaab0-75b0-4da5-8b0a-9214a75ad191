<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>店小秘Token检测测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 8px;
            padding: 24px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .status-card {
            border: 1px solid #d9d9d9;
            border-radius: 6px;
            padding: 16px;
            margin: 16px 0;
        }
        .status-valid {
            border-color: #52c41a;
            background-color: #f6ffed;
        }
        .status-invalid {
            border-color: #ff4d4f;
            background-color: #fff2f0;
        }
        .status-warning {
            border-color: #faad14;
            background-color: #fffbe6;
        }
        .btn {
            background: #1890ff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 4px;
            font-size: 14px;
        }
        .btn:hover {
            background: #40a9ff;
        }
        .btn:disabled {
            background: #d9d9d9;
            cursor: not-allowed;
        }
        .btn-success {
            background: #52c41a;
        }
        .btn-warning {
            background: #faad14;
        }
        .btn-danger {
            background: #ff4d4f;
        }
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 12px;
            margin: 16px 0;
        }
        .info-item {
            background: #fafafa;
            padding: 12px;
            border-radius: 4px;
            border-left: 3px solid #1890ff;
        }
        .info-label {
            font-weight: 600;
            color: #666;
            font-size: 12px;
            margin-bottom: 4px;
        }
        .info-value {
            font-size: 14px;
            color: #333;
        }
        .log-area {
            background: #001529;
            color: #fff;
            padding: 16px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        .cookie-list {
            max-height: 200px;
            overflow-y: auto;
            background: #fafafa;
            padding: 12px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
        }
        .cookie-item {
            margin: 4px 0;
            padding: 4px 8px;
            background: white;
            border-radius: 2px;
            border-left: 3px solid #52c41a;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 店小秘Token检测测试工具</h1>
        <p>此工具用于测试新的店小秘Token检测服务功能</p>
        
        <div class="status-card" id="tokenStatusCard">
            <h3>Token状态</h3>
            <div id="tokenStatus">正在初始化...</div>
            <div class="info-grid" id="tokenInfo" style="display: none;"></div>
        </div>
        
        <div class="container">
            <h3>操作面板</h3>
            <button class="btn" id="checkTokenBtn">🔍 检查Token状态</button>
            <button class="btn" id="refreshTokenBtn">🔄 强制刷新Token</button>
            <button class="btn btn-warning" id="showCookieBtn">🍪 查看Cookie详情</button>
            <button class="btn btn-warning" id="showCacheBtn">📊 查看缓存信息</button>
            <button class="btn btn-danger" id="clearCacheBtn">🗑️ 清除缓存</button>
            <button class="btn" id="testAPIBtn">🧪 测试API调用</button>
        </div>
        
        <div class="container">
            <h3>Cookie信息</h3>
            <div id="cookieInfo" class="cookie-list">点击"查看Cookie详情"按钮查看</div>
        </div>
        
        <div class="container">
            <h3>缓存信息</h3>
            <div id="cacheInfo" class="info-grid">点击"查看缓存信息"按钮查看</div>
        </div>
        
        <div class="container">
            <h3>调试日志</h3>
            <div id="logArea" class="log-area"></div>
            <button class="btn btn-warning" id="clearLogBtn">清除日志</button>
        </div>
    </div>

    <script>
        // 模拟Token检测服务（简化版）
        class TokenDetectionTest {
            constructor() {
                this.STORAGE_KEY = 'dianxiaomi_token_status';
                this.SESSION_STORAGE_KEY = 'dianxiaomi_token_session';
                this.CACHE_DURATION = 5 * 60 * 1000; // 5分钟
                this.init();
            }
            
            init() {
                this.log('Token检测测试工具已初始化');
                this.checkToken(false);
            }
            
            async checkToken(forceRefresh = false) {
                try {
                    this.log(`开始检查Token状态 (强制刷新: ${forceRefresh})`);
                    
                    // 检查缓存
                    if (!forceRefresh) {
                        const cached = this.getCachedStatus();
                        if (cached && !this.isCacheExpired(cached)) {
                            this.log('使用缓存的Token状态');
                            this.updateUI(cached);
                            return cached;
                        }
                    }
                    
                    // 检查Cookie
                    const cookieStatus = this.checkCookies();
                    this.log(`Cookie检查结果: ${cookieStatus.hasDxmCookies ? '发现店小秘Cookie' : '未发现店小秘Cookie'}`);
                    
                    if (!cookieStatus.hasDxmCookies) {
                        const status = {
                            isValid: false,
                            isLoggedIn: false,
                            message: '未检测到店小秘认证信息，请先登录店小秘网站',
                            lastCheckTime: Date.now()
                        };
                        this.saveStatus(status);
                        this.updateUI(status);
                        return status;
                    }
                    
                    // API验证
                    const apiResult = await this.verifyWithAPI();
                    const status = {
                        isValid: apiResult.success,
                        isLoggedIn: apiResult.success,
                        message: apiResult.message,
                        userInfo: apiResult.userInfo,
                        shopCount: apiResult.shopCount,
                        lastCheckTime: Date.now(),
                        error: apiResult.error
                    };
                    
                    this.saveStatus(status);
                    this.updateUI(status);
                    return status;
                    
                } catch (error) {
                    this.log(`Token检测失败: ${error.message}`, 'error');
                    const errorStatus = {
                        isValid: false,
                        isLoggedIn: false,
                        message: `检测失败: ${error.message}`,
                        lastCheckTime: Date.now(),
                        error: error.message
                    };
                    this.updateUI(errorStatus);
                    return errorStatus;
                }
            }
            
            checkCookies() {
                const cookies = this.getCookieDetails();
                const dxmCookieKeys = Object.keys(cookies).filter(key =>
                    key.includes('dxm_') || 
                    key.includes('JSESSIONID') || 
                    key.includes('MYJ_') ||
                    key.includes('dianxiaomi')
                );
                
                return {
                    hasDxmCookies: dxmCookieKeys.length > 0,
                    cookieDetails: cookies,
                    dxmCookieKeys
                };
            }
            
            async verifyWithAPI() {
                try {
                    this.log('调用店小秘用户信息API...');
                    
                    const response = await fetch('https://www.dianxiaomi.com/api/userInfo.json', {
                        method: 'GET',
                        credentials: 'include',
                        headers: {
                            'Accept': 'application/json, text/plain, */*',
                            'X-Requested-With': 'XMLHttpRequest',
                            'User-Agent': navigator.userAgent
                        }
                    });
                    
                    this.log(`API响应状态: ${response.status}`);
                    
                    if (!response.ok) {
                        if (response.status === 401 || response.status === 403) {
                            return {
                                success: false,
                                message: '店小秘未登录，请先登录',
                                error: 'Unauthorized'
                            };
                        }
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }
                    
                    const data = await response.json();
                    this.log(`API响应数据: ${JSON.stringify(data, null, 2)}`);
                    
                    if (data.code === 0 && data.data) {
                        const userInfo = data.data;
                        let shopCount = 0;
                        if (userInfo.shopMap) {
                            shopCount = Object.values(userInfo.shopMap).filter(shop => 
                                shop.platform === 'pddkj'
                            ).length;
                        }
                        
                        return {
                            success: true,
                            message: `店小秘已登录 - 用户: ${userInfo.username || userInfo.name || '未知'}${shopCount > 0 ? `, Temu店铺: ${shopCount}个` : ''}`,
                            userInfo: {
                                id: userInfo.id,
                                username: userInfo.username,
                                name: userInfo.name,
                                phone: userInfo.phone,
                                vipLevel: userInfo.vipLevel
                            },
                            shopCount
                        };
                    } else {
                        return {
                            success: false,
                            message: '店小秘认证无效，请重新登录',
                            error: data.msg || 'Invalid response'
                        };
                    }
                    
                } catch (error) {
                    this.log(`API验证失败: ${error.message}`, 'error');
                    return {
                        success: false,
                        message: `API验证失败: ${error.message}`,
                        error: error.message
                    };
                }
            }
            
            getCookieDetails() {
                const cookies = document.cookie.split(';');
                const cookieObj = {};
                
                cookies.forEach(cookie => {
                    const [name, value] = cookie.trim().split('=');
                    if (name) {
                        cookieObj[name] = value || '';
                    }
                });
                
                return cookieObj;
            }
            
            getCachedStatus() {
                try {
                    // 优先从sessionStorage获取
                    let cached = sessionStorage.getItem(this.SESSION_STORAGE_KEY);
                    if (cached) {
                        return JSON.parse(cached);
                    }
                    
                    // 降级到localStorage
                    cached = localStorage.getItem(this.STORAGE_KEY);
                    if (cached) {
                        return JSON.parse(cached);
                    }
                    
                    return null;
                } catch (error) {
                    this.log(`获取缓存状态失败: ${error.message}`, 'error');
                    return null;
                }
            }
            
            saveStatus(status) {
                try {
                    localStorage.setItem(this.STORAGE_KEY, JSON.stringify(status));
                    sessionStorage.setItem(this.SESSION_STORAGE_KEY, JSON.stringify(status));
                    this.log('Token状态已保存到缓存');
                } catch (error) {
                    this.log(`保存状态失败: ${error.message}`, 'error');
                }
            }
            
            isCacheExpired(status) {
                return Date.now() - status.lastCheckTime > this.CACHE_DURATION;
            }
            
            clearCache() {
                try {
                    localStorage.removeItem(this.STORAGE_KEY);
                    sessionStorage.removeItem(this.SESSION_STORAGE_KEY);
                    this.log('缓存已清除');
                    return true;
                } catch (error) {
                    this.log(`清除缓存失败: ${error.message}`, 'error');
                    return false;
                }
            }
            
            getCacheInfo() {
                const now = Date.now();
                const cached = this.getCachedStatus();
                const cacheAge = cached ? now - cached.lastCheckTime : 0;
                const isExpired = cached ? this.isCacheExpired(cached) : true;
                
                return {
                    hasLocalStorage: !!localStorage.getItem(this.STORAGE_KEY),
                    hasSessionStorage: !!sessionStorage.getItem(this.SESSION_STORAGE_KEY),
                    cacheAge,
                    isExpired,
                    lastCheckTime: cached ? cached.lastCheckTime : 0
                };
            }
            
            updateUI(status) {
                const statusCard = document.getElementById('tokenStatusCard');
                const statusDiv = document.getElementById('tokenStatus');
                const infoDiv = document.getElementById('tokenInfo');
                
                // 更新状态卡片样式
                statusCard.className = 'status-card ' + (status.isValid ? 'status-valid' : 'status-invalid');
                
                // 更新状态文本
                statusDiv.innerHTML = `
                    <div style="font-size: 16px; font-weight: 600; margin-bottom: 8px;">
                        ${status.isValid ? '✅' : '❌'} ${status.message}
                    </div>
                `;
                
                // 更新详细信息
                if (status.userInfo || status.shopCount !== undefined) {
                    infoDiv.style.display = 'grid';
                    infoDiv.innerHTML = `
                        ${status.userInfo ? `
                            <div class="info-item">
                                <div class="info-label">用户名</div>
                                <div class="info-value">${status.userInfo.username || '未知'}</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">姓名</div>
                                <div class="info-value">${status.userInfo.name || '未设置'}</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">VIP等级</div>
                                <div class="info-value">${status.userInfo.vipLevel || 0}</div>
                            </div>
                        ` : ''}
                        ${status.shopCount !== undefined ? `
                            <div class="info-item">
                                <div class="info-label">Temu店铺数</div>
                                <div class="info-value">${status.shopCount}</div>
                            </div>
                        ` : ''}
                        <div class="info-item">
                            <div class="info-label">检查时间</div>
                            <div class="info-value">${new Date(status.lastCheckTime).toLocaleString()}</div>
                        </div>
                    `;
                } else {
                    infoDiv.style.display = 'none';
                }
            }
            
            log(message, level = 'info') {
                const logArea = document.getElementById('logArea');
                const timestamp = new Date().toLocaleTimeString();
                const levelIcon = level === 'error' ? '❌' : level === 'warn' ? '⚠️' : 'ℹ️';
                
                logArea.textContent += `[${timestamp}] ${levelIcon} ${message}\n`;
                logArea.scrollTop = logArea.scrollHeight;
                
                console.log(`[TokenTest] ${message}`);
            }
        }
        
        // 全局实例
        let tokenTest;
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', () => {
            tokenTest = new TokenDetectionTest();

            // 绑定事件监听器
            document.getElementById('checkTokenBtn').addEventListener('click', () => checkToken(false));
            document.getElementById('refreshTokenBtn').addEventListener('click', () => checkToken(true));
            document.getElementById('showCookieBtn').addEventListener('click', showCookieDetails);
            document.getElementById('showCacheBtn').addEventListener('click', showCacheInfo);
            document.getElementById('clearCacheBtn').addEventListener('click', clearCache);
            document.getElementById('testAPIBtn').addEventListener('click', testAPI);
            document.getElementById('clearLogBtn').addEventListener('click', clearLog);
        });
        
        // 全局方法
        async function checkToken(forceRefresh = false) {
            if (tokenTest) {
                await tokenTest.checkToken(forceRefresh);
            }
        }
        
        function showCookieDetails() {
            if (!tokenTest) return;
            
            const cookieStatus = tokenTest.checkCookies();
            const cookieInfo = document.getElementById('cookieInfo');
            
            if (cookieStatus.hasDxmCookies) {
                const cookieList = Object.entries(cookieStatus.cookieDetails)
                    .filter(([key]) => cookieStatus.dxmCookieKeys.includes(key))
                    .map(([key, value]) => `
                        <div class="cookie-item">
                            <strong>${key}:</strong> ${value.substring(0, 50)}${value.length > 50 ? '...' : ''}
                        </div>
                    `).join('');
                
                cookieInfo.innerHTML = `
                    <div style="margin-bottom: 12px; font-weight: 600;">
                        发现 ${cookieStatus.dxmCookieKeys.length} 个店小秘相关Cookie:
                    </div>
                    ${cookieList}
                `;
            } else {
                cookieInfo.innerHTML = '<div style="color: #ff4d4f;">未发现店小秘相关Cookie</div>';
            }
            
            tokenTest.log(`Cookie详情已更新，发现${cookieStatus.dxmCookieKeys.length}个相关Cookie`);
        }
        
        function showCacheInfo() {
            if (!tokenTest) return;
            
            const cacheInfo = tokenTest.getCacheInfo();
            const cacheInfoDiv = document.getElementById('cacheInfo');
            
            cacheInfoDiv.innerHTML = `
                <div class="info-item">
                    <div class="info-label">LocalStorage</div>
                    <div class="info-value">${cacheInfo.hasLocalStorage ? '✅ 有缓存' : '❌ 无缓存'}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">SessionStorage</div>
                    <div class="info-value">${cacheInfo.hasSessionStorage ? '✅ 有缓存' : '❌ 无缓存'}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">缓存年龄</div>
                    <div class="info-value">${Math.round(cacheInfo.cacheAge / 1000)}秒</div>
                </div>
                <div class="info-item">
                    <div class="info-label">是否过期</div>
                    <div class="info-value">${cacheInfo.isExpired ? '❌ 已过期' : '✅ 有效'}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">最后检查</div>
                    <div class="info-value">${cacheInfo.lastCheckTime ? new Date(cacheInfo.lastCheckTime).toLocaleString() : '从未检查'}</div>
                </div>
            `;
            
            tokenTest.log('缓存信息已更新');
        }
        
        function clearCache() {
            if (!tokenTest) return;
            
            if (tokenTest.clearCache()) {
                document.getElementById('cacheInfo').innerHTML = '<div style="color: #52c41a;">缓存已清除</div>';
                // 重新检查状态
                checkToken(true);
            }
        }
        
        async function testAPI() {
            if (!tokenTest) return;
            
            tokenTest.log('开始测试API调用...');
            const result = await tokenTest.verifyWithAPI();
            tokenTest.log(`API测试结果: ${JSON.stringify(result, null, 2)}`);
        }
        
        function clearLog() {
            document.getElementById('logArea').textContent = '';
        }
    </script>
</body>
</html>
