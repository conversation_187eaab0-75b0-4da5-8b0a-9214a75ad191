<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CSP修复测试页面</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 8px;
            padding: 24px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .status-card {
            border: 1px solid #d9d9d9;
            border-radius: 6px;
            padding: 16px;
            margin: 16px 0;
        }
        .status-valid {
            border-color: #52c41a;
            background-color: #f6ffed;
        }
        .status-invalid {
            border-color: #ff4d4f;
            background-color: #fff2f0;
        }
        .btn {
            background: #1890ff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 4px;
            font-size: 14px;
        }
        .btn:hover {
            background: #40a9ff;
        }
        .log-area {
            background: #001529;
            color: #fff;
            padding: 16px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 CSP修复测试页面</h1>
        <p>此页面用于测试CSP（内容安全策略）修复是否成功</p>
        
        <div class="status-card" id="cspStatus">
            <h3>CSP状态检查</h3>
            <div id="cspInfo">正在检查...</div>
        </div>
    </div>

    <div class="container">
        <h3>测试功能</h3>
        <button class="btn" id="testExtensionBtn">🔍 测试扩展环境</button>
        <button class="btn" id="testScriptBtn">📜 测试外部脚本</button>
        <button class="btn" id="clearLogBtn">🗑️ 清除日志</button>
    </div>

    <div class="container">
        <h3>测试日志</h3>
        <div id="logArea" class="log-area"></div>
    </div>

    <script src="test_csp_fix.js"></script>
</body>
</html>
