# 🔄 店小秘Token缓存使用指南

## 🎯 解决的问题

1. ✅ **修复用户信息显示"未知"** - 现在正确显示用户账号
2. ✅ **解决test页面检测失败** - 通过缓存机制实现跨页面复用
3. ✅ **实现token本地缓存** - 支持离线测试和开发

## 📋 使用步骤

### 第一步：在店小秘网站保存Token

1. **登录店小秘网站**
   ```
   https://www.dianxiaomi.com
   ```

2. **打开扩展BasicSettings页面**
   - 点击浏览器扩展图标
   - 进入BasicSettings页面

3. **检测并保存Token**
   - 点击"🔄 刷新"按钮检测Token状态
   - 或点击"💾 保存基础设置"按钮
   - 或使用下拉菜单中的"💾 保存Token到缓存"

4. **确认保存成功**
   - 看到"Token状态已保存到跨页面缓存"提示
   - 用户信息显示正确的账号（不再是"未知"）

### 第二步：在测试页面使用Token

1. **打开测试页面**
   ```
   test_token_detection.html
   ```

2. **检查Token状态**
   - 点击"🔍 检查Token状态"按钮
   - 系统会自动使用缓存的Token

3. **查看缓存信息**
   - 点击"📊 查看缓存信息"按钮
   - 查看"跨页面缓存"部分的详细信息

## 🔧 功能说明

### BasicSettings页面新功能

#### Token状态卡片
- 显示详细的Token状态信息
- 包含用户账号、店铺数量等
- 实时更新状态变化

#### 操作按钮
- **🔄 刷新**: 强制检测Token状态并保存
- **💾 保存Token到缓存**: 手动保存当前Token到跨页面缓存
- **🍪 查看Cookie详情**: 显示相关Cookie信息
- **🗑️ 清除缓存**: 清除所有Token缓存

#### 自动保存机制
- Token检测成功时自动保存
- 保存基础设置时自动保存
- 智能推断成功时自动保存

### 测试页面功能

#### 智能检测
- 自动检查是否在店小秘域名下
- 优先使用跨页面缓存数据
- 降级到本地缓存和API调用

#### 缓存信息显示
- **本地缓存**: SessionStorage和LocalStorage状态
- **跨页面缓存**: 共享的Token状态信息
- **域名检查**: 当前页面域名状态

#### 测试功能
- **🔍 检查Token状态**: 基础检测功能
- **🔄 强制刷新Token**: 忽略缓存重新检测
- **🧠 测试智能推断**: 模拟推断功能
- **🧪 测试API调用**: 直接测试API

## 🛠️ 技术原理

### 缓存架构
```
店小秘网站 → Token检测 → 多层缓存保存
                         ↓
测试页面 ← 缓存读取 ← 跨页面缓存 (dianxiaomi_token_cache)
```

### 数据结构
```javascript
{
  tokenStatus: {
    isValid: true,
    isLoggedIn: true,
    message: "店小秘已登录 - 用户: account_name",
    userInfo: {
      account: "account_name",
      username: "username",
      name: "display_name",
      vipLevel: 1
    },
    shopCount: 1,
    lastCheckTime: **********
  },
  cookies: { /* Cookie信息 */ },
  timestamp: **********,
  version: "1.0.0"
}
```

### 字段优先级
```
用户显示名称优先级:
1. account (主要标识)
2. username (备选)
3. name (显示名)
4. accountAlias (别名)
```

## 🔍 调试方法

### 1. 浏览器控制台
```javascript
// 查看跨页面缓存
console.log(JSON.parse(localStorage.getItem('dianxiaomi_token_cache')));

// 查看会话缓存
console.log(JSON.parse(sessionStorage.getItem('dianxiaomi_token_session')));
```

### 2. 测试页面调试
- 使用"查看缓存信息"功能
- 查看调试日志输出
- 检查跨页面缓存状态

### 3. BasicSettings调试
- 查看Token状态卡片
- 使用Cookie详情功能
- 检查浏览器开发者工具

## ⚠️ 注意事项

### 缓存过期
- 默认30分钟过期时间
- 过期后需要重新在店小秘网站保存
- 自动清理过期缓存

### 域名限制
- 实际API调用需要在店小秘域名下
- 测试页面只能使用缓存数据
- 跨域无法直接操作Cookie

### 版本兼容
- 缓存包含版本号管理
- 版本不兼容时自动清除
- 确保数据结构一致性

## 🎉 效果验证

### 修复前
```
❌ BasicSettings显示"用户: 未知"
❌ test页面无法检测token
❌ 页面间无法共享状态
```

### 修复后
```
✅ BasicSettings显示"用户: [实际账号]"
✅ test页面显示"来自缓存"的token状态
✅ 支持跨页面token复用
✅ 支持离线调试和测试
```

## 🔮 扩展应用

### 其他测试页面集成
```javascript
// 在任何页面使用
const cached = localStorage.getItem('dianxiaomi_token_cache');
if (cached) {
    const cacheData = JSON.parse(cached);
    const tokenStatus = cacheData.tokenStatus;
    if (tokenStatus.isValid) {
        // 使用token进行操作
        console.log('用户:', tokenStatus.userInfo?.account);
    }
}
```

### 开发环境使用
- 本地开发时可以使用缓存
- 无需每次都在店小秘网站登录
- 提高开发和测试效率

现在您可以：
1. 在BasicSettings看到正确的用户信息
2. 在test页面使用缓存的token
3. 在不同页面间共享token状态
4. 进行离线调试和开发

请按照使用步骤操作，享受新的Token缓存功能！
