// Sample code if using extensionpay.com
// import { extPay } from 'src/utils/payment/extPay'
// extPay.startBackground()

// 导入JSZip库以支持ZIP文件创建
import '../lib/jszip.min.js'

// 声明JSZip全局变量
declare const JSZip: any

chrome.runtime.onInstalled.addListener(async (opt) => {
  // Check if reason is install or update. Eg: opt.reason === 'install' // If extension is installed.
  // opt.reason === 'update' // If extension is updated.
  if (opt.reason === "install") {
    // 首次安装时，打开 side-panel 而不是 setup 页面
    try {
      // 尝试打开 side panel
      await chrome.sidePanel.open({ windowId: (await chrome.windows.getCurrent()).id })
    } catch (error) {
      // 如果 side panel 不可用，则打开 setup 页面
      chrome.tabs.create({
        active: true,
        url: chrome.runtime.getURL("src/ui/setup/index.html#/setup/install"),
      })
    }

    return
  }

  if (opt.reason === "update") {
    // 更新时，也打开 side-panel 而不是 setup 页面
    try {
      // 尝试打开 side panel
      await chrome.sidePanel.open({ windowId: (await chrome.windows.getCurrent()).id })
    } catch (error) {
      // 如果 side panel 不可用，则不做任何操作
      console.info('Side panel not available, skipping auto-open')
    }

    return
  }
})

self.onerror = function (message, source, lineno, colno, error) {
  console.info("Error: " + message)
  console.info("Source: " + source)
  console.info("Line: " + lineno)
  console.info("Column: " + colno)
  console.info("Error object: " + error)
}

console.info("hello world from background")

// Temu 数据获取服务
class TemuBackgroundService {
  // 获取 Temu 店铺信息
  async getTemuShopInfo(): Promise<{ success: boolean; data?: any; error?: string }> {
    try {
      console.info('[Background] 开始获取 Temu 店铺信息...')

      // 1. 查找 Temu 商家后台标签页
      const temuTab = await this.findTemuSellerTab()
      if (!temuTab) {
        return {
          success: false,
          error: '未找到 Temu 商家后台标签页，请先打开并登录 Temu 商家后台'
        }
      }

      console.info('[Background] 找到 Temu 标签页:', temuTab.url)

      // 2. 向 content script 发送消息获取数据
      try {
        const response = await chrome.tabs.sendMessage(temuTab.id!, {
          action: 'GET_TEMU_INFO'
        })

        if (response && response.success) {
          console.info('[Background] 成功获取 Temu 信息:', response.data)
          return {
            success: true,
            data: response.data
          }
        } else {
          return {
            success: false,
            error: response?.error || '获取 Temu 信息失败'
          }
        }
      } catch (messageError) {
        console.warn('[Background] Content script 通信失败，尝试注入脚本...')

        // 3. 如果 content script 不可用，尝试注入脚本
        return await this.injectAndGetTemuInfo(temuTab.id!)
      }
    } catch (error) {
      console.error('[Background] 获取 Temu 店铺信息失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '未知错误'
      }
    }
  }

  // 查找 Temu 商家后台标签页
  private async findTemuSellerTab(): Promise<chrome.tabs.Tab | null> {
    const tabs = await chrome.tabs.query({})

    const temuDomains = [
      'seller.temu.com',
      'seller.kuajingmaihuo.com',
      'seller-cn.temu.com',
      'agentseller.temu.com',
      'agentseller-us.temu.com'
    ]

    for (const tab of tabs) {
      if (tab.url) {
        const url = new URL(tab.url)
        if (temuDomains.some(domain => url.hostname.includes(domain))) {
          return tab
        }
      }
    }

    return null
  }

  // 注入脚本并获取 Temu 信息
  private async injectAndGetTemuInfo(tabId: number): Promise<{ success: boolean; data?: any; error?: string }> {
    try {
      console.info('[Background] 注入脚本获取 Temu 信息...')

      // 注入脚本到页面
      const results = await chrome.scripting.executeScript({
        target: { tabId },
        func: this.getTemuInfoFromPage
      })

      if (results && results[0] && results[0].result) {
        const result = results[0].result
        if (result.success) {
          console.info('[Background] 注入脚本成功获取数据:', result.data)
          return result
        } else {
          return {
            success: false,
            error: result.error || '注入脚本执行失败'
          }
        }
      } else {
        return {
          success: false,
          error: '注入脚本无返回结果'
        }
      }
    } catch (error) {
      console.error('[Background] 注入脚本失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '注入脚本失败'
      }
    }
  }

  // 在页面中执行的函数（会被注入到 Temu 页面）
  private getTemuInfoFromPage(): { success: boolean; data?: any; error?: string } {
    try {
      console.info('[Injected Script] 开始获取 Temu 信息...')

      // 获取 anti-content 头部
      const getAntiContent = (): string | null => {
        try {
          // 方法1: 从全局变量获取
          const win = window as any
          if (win.antiContent) return win.antiContent
          if (win.__INITIAL_STATE__?.antiContent) return win.__INITIAL_STATE__.antiContent

          // 方法2: 从脚本中搜索
          const scripts = Array.from(document.querySelectorAll('script'))
          for (const script of scripts) {
            const content = script.textContent || script.innerHTML
            const patterns = [
              /["']anti-content["']\s*:\s*["']([^"']+)["']/i,
              /antiContent\s*:\s*["']([^"']+)["']/i,
              /"anti-content":\s*"([^"]+)"/i
            ]

            for (const pattern of patterns) {
              const match = content.match(pattern)
              if (match) return match[1]
            }
          }
          return null
        } catch (error) {
          console.warn('[Injected Script] 获取 anti-content 失败:', error)
          return null
        }
      }

      // 调用 Temu API
      const antiContent = getAntiContent()
      console.info('[Injected Script] Anti-content:', antiContent ? '已获取' : '未找到')

      const headers: Record<string, string> = {
        'Accept': '*/*',
        'Accept-Language': 'zh-CN,zh;q=0.9',
        'Content-Type': 'application/json',
        'Cache-Control': 'max-age=0'
      }

      if (antiContent) {
        headers['anti-content'] = antiContent
      }

      // 返回 Promise（注意：这个函数会在页面上下文中执行）
      return fetch('https://seller.kuajingmaihuo.com/bg/quiet/api/mms/userInfo', {
        method: 'POST',
        credentials: 'include',
        headers,
        body: JSON.stringify({})
      })
      .then(response => response.json())
      .then(data => {
        console.info('[Injected Script] API 响应:', data)
        if (data.success && data.result) {
          return {
            success: true,
            data: data.result
          }
        } else {
          return {
            success: false,
            error: 'API 返回数据格式错误'
          }
        }
      })
      .catch(error => {
        console.error('[Injected Script] API 调用失败:', error)
        return {
          success: false,
          error: error.message || 'API 调用失败'
        }
      })
    } catch (error) {
      console.error('[Injected Script] 执行失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '执行失败'
      }
    }
  }
}

// 店小秘后台服务
class DianxiaomiBackgroundService {
  // 调用店小秘API
  async callDianxiaomiAPI(apiConfig: {
    url: string
    method?: string
    data?: any
    headers?: Record<string, string>
  }): Promise<{ success: boolean; data?: any; error?: string }> {
    try {
      console.info('[Background] 调用店小秘API:', apiConfig.url)

      // 1. 查找店小秘标签页
      const dxmTab = await this.findDianxiaomiTab()
      if (!dxmTab) {
        return {
          success: false,
          error: '未找到店小秘标签页，请先打开并登录店小秘网站'
        }
      }

      console.info('[Background] 找到店小秘标签页:', dxmTab.url)

      // 2. 向 content script 发送消息调用API
      try {
        const response = await chrome.tabs.sendMessage(dxmTab.id!, {
          action: 'CALL_DIANXIAOMI_API',
          apiConfig
        })

        if (response && response.success) {
          console.info('[Background] 店小秘API调用成功:', response.data)
          return response
        } else {
          return {
            success: false,
            error: response?.error || '店小秘API调用失败'
          }
        }
      } catch (messageError) {
        console.warn('[Background] Content script 通信失败，尝试注入脚本...')

        // 3. 如果 content script 不可用，尝试注入脚本
        return await this.injectAndCallAPI(dxmTab.id!, apiConfig)
      }
    } catch (error) {
      console.error('[Background] 调用店小秘API失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '未知错误'
      }
    }
  }

  // 查找店小秘标签页
  private async findDianxiaomiTab(): Promise<chrome.tabs.Tab | null> {
    const tabs = await chrome.tabs.query({})

    const dxmDomains = [
      'dianxiaomi.com',
      'www.dianxiaomi.com'
    ]

    for (const tab of tabs) {
      if (tab.url) {
        const url = new URL(tab.url)
        if (dxmDomains.some(domain => url.hostname.includes(domain))) {
          return tab
        }
      }
    }

    return null
  }

  // 注入脚本并调用API
  private async injectAndCallAPI(tabId: number, apiConfig: any): Promise<{ success: boolean; data?: any; error?: string }> {
    try {
      console.info('[Background] 注入脚本调用店小秘API...')

      // 注入脚本到页面
      const results = await chrome.scripting.executeScript({
        target: { tabId },
        func: this.callAPIFromPage,
        args: [apiConfig]
      })

      if (results && results[0] && results[0].result) {
        const result = results[0].result
        if (result.success) {
          console.info('[Background] 注入脚本成功调用API:', result.data)
          return result
        } else {
          return {
            success: false,
            error: result.error || '注入脚本执行失败'
          }
        }
      } else {
        return {
          success: false,
          error: '注入脚本无返回结果'
        }
      }
    } catch (error) {
      console.error('[Background] 注入脚本失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '注入脚本失败'
      }
    }
  }

  // 在页面中执行的函数（会被注入到店小秘页面）
  private async callAPIFromPage(apiConfig: {
    url: string
    method?: string
    data?: any
    headers?: Record<string, string>
  }): Promise<{ success: boolean; data?: any; error?: string }> {
    try {
      console.info('[Injected Script] 调用店小秘API:', apiConfig.url)

      const defaultHeaders = {
        'Accept': 'application/json, text/plain, */*',
        'Content-Type': 'application/json',
        'X-Requested-With': 'XMLHttpRequest'
      }

      const headers = { ...defaultHeaders, ...apiConfig.headers }
      const method = apiConfig.method || 'GET'

      const fetchOptions: RequestInit = {
        method,
        credentials: 'include',
        headers
      }

      if (method !== 'GET' && apiConfig.data) {
        fetchOptions.body = JSON.stringify(apiConfig.data)
      }

      const response = await fetch(apiConfig.url, fetchOptions)

      console.info('[Injected Script] API响应状态:', response.status)

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const data = await response.json()
      console.info('[Injected Script] API响应数据:', data)

      return {
        success: true,
        data
      }
    } catch (error) {
      console.error('[Injected Script] API调用失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'API调用失败'
      }
    }
  }

  // 获取店小秘Token状态
  async getDianxiaomiTokenStatus(): Promise<{ success: boolean; data?: any; error?: string }> {
    return this.callDianxiaomiAPI({
      url: 'https://www.dianxiaomi.com/api/userInfo.json',
      method: 'GET'
    })
  }

  // 获取店铺列表
  async getShopList(): Promise<{ success: boolean; data?: any; error?: string }> {
    return this.callDianxiaomiAPI({
      url: 'https://www.dianxiaomi.com/shop/list/pddkj.htm',
      method: 'GET'
    })
  }

  // 获取运费模板
  async getFreightTemplates(shopId: string): Promise<{ success: boolean; data?: any; error?: string }> {
    return this.callDianxiaomiAPI({
      url: 'https://www.dianxiaomi.com/api/freight/template/list.json',
      method: 'POST',
      data: { shopId }
    })
  }

  // 获取商品分类
  async getProductCategories(shopId: string): Promise<{ success: boolean; data?: any; error?: string }> {
    return this.callDianxiaomiAPI({
      url: 'https://www.dianxiaomi.com/api/category/list.json',
      method: 'POST',
      data: { shopId }
    })
  }

  // 上传商品
  async uploadProduct(productData: any): Promise<{ success: boolean; data?: any; error?: string }> {
    return this.callDianxiaomiAPI({
      url: 'https://www.dianxiaomi.com/api/product/upload.json',
      method: 'POST',
      data: productData
    })
  }

  // 创建ZIP文件 - 现在可以在background中使用JSZip了
  async createZipFile(jsonData: string): Promise<{ success: boolean; zipBlob?: Blob; size?: number; error?: string }> {
    try {
      console.info('[Background] 开始创建ZIP文件...')

      // 检查JSZip是否可用
      if (typeof JSZip === 'undefined') {
        throw new Error('JSZip库不可用')
      }

      // 创建ZIP实例
      const zip = new JSZip()

      // 将JSON数据添加到ZIP中作为choiceSave.txt
      zip.file('choiceSave.txt', jsonData)

      console.info('[Background] 正在生成ZIP文件...')

      // 生成ZIP文件
      const zipBlob = await zip.generateAsync({
        type: 'blob',
        compression: 'DEFLATE',
        compressionOptions: {
          level: 6
        }
      })

      console.info(`[Background] ZIP文件创建成功，大小: ${zipBlob.size} bytes`)

      return {
        success: true,
        zipBlob,
        size: zipBlob.size
      }
    } catch (error) {
      console.error('[Background] 创建ZIP文件失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'ZIP文件创建失败'
      }
    }
  }

  // 上传店小秘商品 - 完全借鉴test_upload.html的逻辑，直接使用新的ZIP上传方法
  async uploadDianxiaomiProduct(jsonData: string): Promise<{ success: boolean; data?: any; error?: string; status?: number; headers?: any }> {
    try {
      console.info('[Background] 开始上传店小秘商品...')
      console.info('[Background] JSON数据大小:', jsonData.length, 'bytes')
      console.info('[Background] 使用新的上传流程：Background创建ZIP → 注入脚本上传')

      // 1. 查找店小秘标签页
      const dxmTab = await this.findDianxiaomiTab()
      if (!dxmTab) {
        return {
          success: false,
          error: '未找到店小秘标签页，请先打开并登录店小秘网站'
        }
      }

      console.info('[Background] 找到店小秘标签页:', dxmTab.url)

      // 2. 在background中创建ZIP文件
      console.info('[Background] 在background中创建ZIP文件...')
      const zipResult = await this.createZipFile(jsonData)
      if (!zipResult.success) {
        return {
          success: false,
          error: `ZIP文件创建失败: ${zipResult.error}`
        }
      }

      console.info(`[Background] ZIP文件创建成功，大小: ${zipResult.size} bytes`)

      // 3. 注入脚本直接上传ZIP文件
      console.info('[Background] 注入脚本上传ZIP文件...')
      return await this.injectAndUploadZipFile(dxmTab.id!, zipResult.zipBlob!)
    } catch (error) {
      console.error('[Background] 上传店小秘商品失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '上传失败'
      }
    }
  }

  // 注入脚本并上传商品
  private async injectAndUploadProduct(tabId: number, jsonData: string): Promise<{ success: boolean; data?: any; error?: string; status?: number; headers?: any }> {
    try {
      console.info('[Background] 注入脚本上传店小秘商品...')

      // 注入脚本到页面
      const results = await chrome.scripting.executeScript({
        target: { tabId },
        func: this.uploadProductFromPage,
        args: [jsonData]
      })

      if (results && results[0] && results[0].result) {
        const result = results[0].result
        if (result.success) {
          console.info('[Background] 注入脚本成功上传商品:', result)
          return result
        } else {
          return {
            success: false,
            error: result.error || '注入脚本执行失败'
          }
        }
      } else {
        return {
          success: false,
          error: '注入脚本无返回结果'
        }
      }
    } catch (error) {
      console.error('[Background] 注入脚本失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '注入脚本失败'
      }
    }
  }

  // 注入脚本并上传ZIP文件（ZIP已在background中创建）
  private async injectAndUploadZipFile(tabId: number, zipBlob: Blob): Promise<{ success: boolean; data?: any; error?: string; status?: number; headers?: any }> {
    try {
      console.info('[Background] 注入脚本上传ZIP文件...')
      console.info('[Background] ZIP文件大小:', zipBlob.size, 'bytes')

      // 将Blob转换为ArrayBuffer，以便传递给注入脚本
      const arrayBuffer = await zipBlob.arrayBuffer()
      const uint8Array = new Uint8Array(arrayBuffer)

      // 注入脚本到页面 - 使用包装函数处理异步
      const results = await chrome.scripting.executeScript({
        target: { tabId },
        func: this.uploadZipFromPageWrapper,
        args: [Array.from(uint8Array)] // 转换为普通数组以便传递
      })

      if (results && results[0] && results[0].result) {
        const result = results[0].result
        if (result.success) {
          console.info('[Background] 注入脚本成功上传ZIP:', result)
          return result
        } else {
          return {
            success: false,
            error: result.error || '注入脚本执行失败'
          }
        }
      } else {
        return {
          success: false,
          error: '注入脚本无返回结果'
        }
      }
    } catch (error) {
      console.error('[Background] 注入脚本上传ZIP失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '注入脚本失败'
      }
    }
  }

  // 包装函数，用于在注入脚本中处理异步上传
  private uploadZipFromPageWrapper(zipData: number[]) {
    // 这个函数会在页面环境中执行
    return (async () => {
      try {
        console.info('[Injected Script] 开始上传ZIP文件...')
        console.info('[Injected Script] ZIP数据大小:', zipData.length, 'bytes')

        // 将数组转换回Blob
        const uint8Array = new Uint8Array(zipData)
        const zipBlob = new Blob([uint8Array], { type: 'application/zip' })

        console.info('[Injected Script] ZIP Blob创建成功，大小:', zipBlob.size, 'bytes')

        // 创建FormData - 完全按照test_upload.html的方式
        const formData = new FormData()
        formData.append('file', zipBlob, 'blob')
        formData.append('op', '1')

        console.info('[Injected Script] 发送上传请求...')

        // 发送请求 - 完全按照test_upload.html的方式
        const response = await fetch('https://www.dianxiaomi.com/api/popTemuProduct/add.json', {
          method: 'POST',
          body: formData,
          credentials: 'include',
          headers: {
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9',
            'Origin': 'https://www.dianxiaomi.com',
            'Referer': 'https://www.dianxiaomi.com/web/popTemu/edit',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin',
            'User-Agent': navigator.userAgent
          }
        })

        console.info('[Injected Script] 响应状态:', response.status, response.statusText)

        const responseText = await response.text()
        console.info('[Injected Script] 响应内容:', responseText)

        // 收集响应头
        const headers = {}
        for (const [key, value] of response.headers.entries()) {
          headers[key] = value
        }

        let responseData = responseText
        try {
          responseData = JSON.parse(responseText)
        } catch (e) {
          // 如果不是JSON，保持原文本
        }

        return {
          success: response.ok,
          data: responseData,
          status: response.status,
          headers,
          error: response.ok ? undefined : `HTTP ${response.status}: ${response.statusText}`
        }
      } catch (error) {
        console.error('[Injected Script] 上传ZIP失败:', error)
        return {
          success: false,
          error: error instanceof Error ? error.message : '上传失败'
        }
      }
    })()
  }

  // 在页面中执行的ZIP上传函数（ZIP已创建，直接上传）
  private uploadZipFromPage(zipData: number[]): Promise<{ success: boolean; data?: any; error?: string; status?: number; headers?: any }> {
    try {
      console.info('[Injected Script] 开始上传ZIP文件...')
      console.info('[Injected Script] ZIP数据大小:', zipData.length, 'bytes')

      // 将数组转换回Blob
      const uint8Array = new Uint8Array(zipData)
      const zipBlob = new Blob([uint8Array], { type: 'application/zip' })

      console.info('[Injected Script] ZIP Blob创建成功，大小:', zipBlob.size, 'bytes')

      // 创建FormData - 完全按照test_upload.html的方式
      const formData = new FormData()
      formData.append('file', zipBlob, 'blob')
      formData.append('op', '1')

      console.info('[Injected Script] 发送上传请求...')

      // 发送请求 - 完全按照test_upload.html的方式
      const response = await fetch('https://www.dianxiaomi.com/api/popTemuProduct/add.json', {
        method: 'POST',
        body: formData,
        credentials: 'include',
        headers: {
          'Accept': 'application/json, text/plain, */*',
          'Accept-Language': 'zh-CN,zh;q=0.9',
          'Origin': 'https://www.dianxiaomi.com',
          'Referer': 'https://www.dianxiaomi.com/web/popTemu/edit',
          'Sec-Fetch-Dest': 'empty',
          'Sec-Fetch-Mode': 'cors',
          'Sec-Fetch-Site': 'same-origin',
          'User-Agent': navigator.userAgent
        }
      })

      console.info('[Injected Script] 响应状态:', response.status, response.statusText)

      const responseText = await response.text()
      console.info('[Injected Script] 响应内容:', responseText)

      // 收集响应头
      const headers: Record<string, string> = {}
      for (const [key, value] of response.headers.entries()) {
        headers[key] = value
      }

      let responseData: any = responseText
      try {
        responseData = JSON.parse(responseText)
      } catch (e) {
        // 如果不是JSON，保持原文本
      }

      return {
        success: response.ok,
        data: responseData,
        status: response.status,
        headers,
        error: response.ok ? undefined : `HTTP ${response.status}: ${response.statusText}`
      }
    } catch (error) {
      console.error('[Injected Script] 上传ZIP失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '上传失败'
      }
    }
  }

  // 在页面中执行的上传函数（会被注入到店小秘页面）
  private async uploadProductFromPage(jsonData: string): Promise<{ success: boolean; data?: any; error?: string; status?: number; headers?: any }> {
    try {
      console.info('[Injected Script] 开始上传店小秘商品...')
      console.info('[Injected Script] JSON数据大小:', jsonData.length, 'bytes')

      // 检查并加载JSZip库
      let JSZip = (window as any).JSZip
      if (!JSZip) {
        console.info('[Injected Script] JSZip库不可用，尝试动态加载...')

        // 尝试动态加载JSZip
        try {
          const script = document.createElement('script')
          script.src = 'https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js'
          document.head.appendChild(script)

          // 等待脚本加载
          await new Promise((resolve, reject) => {
            script.onload = resolve
            script.onerror = reject
            setTimeout(reject, 10000) // 10秒超时
          })

          JSZip = (window as any).JSZip
          if (!JSZip) {
            throw new Error('JSZip加载后仍不可用')
          }

          console.info('[Injected Script] JSZip库动态加载成功')
        } catch (loadError) {
          throw new Error(`JSZip库加载失败: ${loadError}。请确保网络连接正常或页面已预加载JSZip库`)
        }
      }

      const zip = new JSZip()
      zip.file('choiceSave.txt', jsonData)

      const zipBlob = await zip.generateAsync({
        type: 'blob',
        compression: 'DEFLATE',
        compressionOptions: {
          level: 6
        }
      })

      console.info('[Injected Script] ZIP文件创建成功，大小:', zipBlob.size, 'bytes')

      // 创建FormData
      const formData = new FormData()
      formData.append('file', zipBlob, 'blob')
      formData.append('op', '1')

      console.info('[Injected Script] 发送上传请求...')

      // 发送请求 - 完全按照test_upload.html的方式
      const response = await fetch('https://www.dianxiaomi.com/api/popTemuProduct/add.json', {
        method: 'POST',
        body: formData,
        credentials: 'include',
        headers: {
          'Accept': 'application/json, text/plain, */*',
          'Accept-Language': 'zh-CN,zh;q=0.9',
          'Origin': 'https://www.dianxiaomi.com',
          'Referer': 'https://www.dianxiaomi.com/web/popTemu/edit',
          'Sec-Fetch-Dest': 'empty',
          'Sec-Fetch-Mode': 'cors',
          'Sec-Fetch-Site': 'same-origin',
          'User-Agent': navigator.userAgent
        }
      })

      console.info('[Injected Script] 响应状态:', response.status, response.statusText)

      const responseText = await response.text()
      console.info('[Injected Script] 响应内容:', responseText)

      // 收集响应头
      const headers: Record<string, string> = {}
      for (const [key, value] of response.headers.entries()) {
        headers[key] = value
      }

      let responseData: any = responseText
      try {
        responseData = JSON.parse(responseText)
      } catch (e) {
        // 如果不是JSON，保持原文本
      }

      return {
        success: response.ok,
        data: responseData,
        status: response.status,
        headers,
        error: response.ok ? undefined : `HTTP ${response.status}: ${response.statusText}`
      }
    } catch (error) {
      console.error('[Injected Script] 上传失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '上传失败'
      }
    }
  }
}

// 创建服务实例
const temuService = new TemuBackgroundService()
const dianxiaomiService = new DianxiaomiBackgroundService()

// 监听来自 side panel 的消息
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  console.info('[Background] 收到消息:', request)

  if (request.action === 'GET_TEMU_SHOP_INFO') {
    // 异步获取 Temu 店铺信息
    temuService.getTemuShopInfo()
      .then(result => {
        console.info('[Background] 获取结果:', result)
        sendResponse(result)
      })
      .catch(error => {
        console.error('[Background] 获取失败:', error)
        sendResponse({
          success: false,
          error: error.message || '获取失败'
        })
      })

    // 返回 true 表示异步响应
    return true
  }

  if (request.action === 'SAVE_PRODUCT_CONFIG') {
    // 处理商品配置保存
    console.info('[Background] 收到商品配置保存请求:', request.config)

    try {
      // 这里可以添加额外的处理逻辑，比如验证配置数据
      const config = request.config

      if (config && config.form && config.form.shopId && config.form.categoryId) {
        // 配置数据有效，可以进行保存
        console.info('[Background] 商品配置数据有效，准备保存:', {
          shopId: config.form.shopId,
          categoryId: config.form.categoryId,
          categoryName: config.form.categoryName
        })

        sendResponse({
          success: true,
          message: '商品配置已接收并处理',
          data: {
            shopId: config.form.shopId,
            categoryId: config.form.categoryId,
            timestamp: new Date().toISOString()
          }
        })
      } else {
        console.warn('[Background] 商品配置数据格式不正确:', config)
        sendResponse({
          success: false,
          error: '配置数据格式不正确'
        })
      }
    } catch (error) {
      console.error('[Background] 处理商品配置保存失败:', error)
      sendResponse({
        success: false,
        error: error instanceof Error ? error.message : '处理失败'
      })
    }

    return true
  }

  // 处理店小秘API调用请求
  if (request.action === 'CALL_DIANXIAOMI_API') {
    console.info('[Background] 收到店小秘API调用请求:', request.apiConfig)

    dianxiaomiService.callDianxiaomiAPI(request.apiConfig)
      .then(result => {
        console.info('[Background] 店小秘API调用结果:', result)
        sendResponse(result)
      })
      .catch(error => {
        console.error('[Background] 店小秘API调用失败:', error)
        sendResponse({
          success: false,
          error: error.message || '调用失败'
        })
      })

    return true
  }

  // 处理获取店小秘Token状态请求
  if (request.action === 'GET_DIANXIAOMI_TOKEN_STATUS') {
    console.info('[Background] 收到获取店小秘Token状态请求')

    dianxiaomiService.getDianxiaomiTokenStatus()
      .then(result => {
        console.info('[Background] 获取Token状态结果:', result)
        sendResponse(result)
      })
      .catch(error => {
        console.error('[Background] 获取Token状态失败:', error)
        sendResponse({
          success: false,
          error: error.message || '获取失败'
        })
      })

    return true
  }

  // 处理获取店铺列表请求
  if (request.action === 'GET_DIANXIAOMI_SHOP_LIST') {
    console.info('[Background] 收到获取店铺列表请求')

    dianxiaomiService.getShopList()
      .then(result => {
        console.info('[Background] 获取店铺列表结果:', result)
        sendResponse(result)
      })
      .catch(error => {
        console.error('[Background] 获取店铺列表失败:', error)
        sendResponse({
          success: false,
          error: error.message || '获取失败'
        })
      })

    return true
  }

  // 处理获取运费模板请求
  if (request.action === 'GET_FREIGHT_TEMPLATES') {
    console.info('[Background] 收到获取运费模板请求:', request.shopId)

    dianxiaomiService.getFreightTemplates(request.shopId)
      .then(result => {
        console.info('[Background] 获取运费模板结果:', result)
        sendResponse(result)
      })
      .catch(error => {
        console.error('[Background] 获取运费模板失败:', error)
        sendResponse({
          success: false,
          error: error.message || '获取失败'
        })
      })

    return true
  }

  // 处理获取商品分类请求
  if (request.action === 'GET_PRODUCT_CATEGORIES') {
    console.info('[Background] 收到获取商品分类请求:', request.shopId)

    dianxiaomiService.getProductCategories(request.shopId)
      .then(result => {
        console.info('[Background] 获取商品分类结果:', result)
        sendResponse(result)
      })
      .catch(error => {
        console.error('[Background] 获取商品分类失败:', error)
        sendResponse({
          success: false,
          error: error.message || '获取失败'
        })
      })

    return true
  }

  // 处理商品上传请求
  if (request.action === 'UPLOAD_PRODUCT') {
    console.info('[Background] 收到商品上传请求:', request.productData)

    dianxiaomiService.uploadProduct(request.productData)
      .then(result => {
        console.info('[Background] 商品上传结果:', result)
        sendResponse(result)
      })
      .catch(error => {
        console.error('[Background] 商品上传失败:', error)
        sendResponse({
          success: false,
          error: error.message || '上传失败'
        })
      })

    return true
  }

  // 处理创建ZIP文件请求 - 现在可以在background中使用JSZip了
  if (request.action === 'CREATE_ZIP_FILE') {
    console.info('[Background] 收到创建ZIP文件请求')

    dianxiaomiService.createZipFile(request.jsonData)
      .then(result => {
        console.info('[Background] ZIP文件创建结果:', result)
        sendResponse(result)
      })
      .catch(error => {
        console.error('[Background] ZIP文件创建失败:', error)
        sendResponse({
          success: false,
          error: error.message || '创建失败'
        })
      })

    return true
  }

  // 处理店小秘商品上传请求 - 借鉴test_upload.html逻辑
  if (request.action === 'UPLOAD_DIANXIAOMI_PRODUCT') {
    console.info('[Background] 收到店小秘商品上传请求')

    dianxiaomiService.uploadDianxiaomiProduct(request.jsonData)
      .then(result => {
        console.info('[Background] 店小秘商品上传结果:', result)
        sendResponse(result)
      })
      .catch(error => {
        console.error('[Background] 店小秘商品上传失败:', error)
        sendResponse({
          success: false,
          error: error.message || '上传失败'
        })
      })

    return true
  }

  // 处理JSZip可用性测试请求
  if (request.action === 'TEST_JSZIP_AVAILABILITY') {
    console.info('[Background] 收到JSZip可用性测试请求')

    try {
      // 检查JSZip是否可用
      if (typeof JSZip === 'undefined') {
        sendResponse({
          success: false,
          error: 'JSZip库不可用，请检查导入配置'
        })
      } else {
        // 测试JSZip基本功能
        const zip = new JSZip()
        zip.file('test.txt', 'JSZip test successful!')

        sendResponse({
          success: true,
          version: JSZip.version || '未知版本',
          message: 'JSZip库在background中可用'
        })

        console.info('[Background] JSZip可用性测试成功')
      }
    } catch (error) {
      console.error('[Background] JSZip可用性测试失败:', error)
      sendResponse({
        success: false,
        error: error instanceof Error ? error.message : 'JSZip测试失败'
      })
    }

    return true
  }

  return false
})

export {}
