<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Amazon搜索页面测试 - 批量采集和单品采集功能</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .search-results {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .product-item {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            background: white;
            transition: all 0.3s ease;
        }
        
        .product-item:hover {
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        
        .s-product-image-container {
            position: relative;
            margin-bottom: 10px;
        }
        
        .s-image {
            width: 100%;
            height: 200px;
            object-fit: cover;
            border-radius: 4px;
        }
        
        .product-title {
            font-size: 14px;
            font-weight: 500;
            margin: 10px 0;
            line-height: 1.4;
        }
        
        .product-price {
            color: #B12704;
            font-weight: bold;
            font-size: 16px;
        }
        
        .product-rating {
            display: flex;
            align-items: center;
            gap: 5px;
            margin: 5px 0;
            font-size: 12px;
        }
        
        .stars {
            color: #FFA41C;
        }
        
        .test-info {
            background: #e6f7ff;
            border: 1px solid #91d5ff;
            border-radius: 6px;
            padding: 15px;
            margin-bottom: 20px;
        }
        
        .test-info h3 {
            margin: 0 0 10px 0;
            color: #1890ff;
        }
        
        .test-info ul {
            margin: 10px 0;
            padding-left: 20px;
        }
        
        .test-info li {
            margin: 5px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="test-info">
            <h3>🧪 Amazon搜索页面功能测试</h3>
            <p>此页面用于测试Amazon搜索页面的批量采集和单品采集功能：</p>
            <ul>
                <li><strong>批量采集按钮</strong>：应该出现在页面右上角，红色背景，显示匹配的商品数量</li>
                <li><strong>单品采集按钮</strong>：固定显示在每个商品图片的右上角，红色的"单品采集"按钮</li>
                <li><strong>URL模拟</strong>：页面URL包含amazon.com/s，模拟真实的Amazon搜索页面</li>
            </ul>
            <p><strong>注意</strong>：请确保已安装Chrome扩展并启用，然后刷新此页面查看效果。</p>
        </div>
        
        <h1>Garden Tools & Equipment</h1>
        <p>1-16 of over 50,000 results for "garden tools"</p>
        
        <div class="search-results">
            <!-- 商品1 -->
            <div class="product-item" data-asin="B08XQJH123" data-component-type="s-search-result">
                <div class="s-product-image-container">
                    <img class="s-image" src="https://via.placeholder.com/200x200/4CAF50/white?text=Garden+Tool+1" alt="Garden Tool Set">
                </div>
                <h3 class="product-title">
                    <a href="/dp/B08XQJH123">Professional Garden Tool Set - 10 Piece Heavy Duty Gardening Kit</a>
                </h3>
                <div class="product-rating">
                    <span class="stars">★★★★☆</span>
                    <span>4.5 (2,341)</span>
                </div>
                <div class="product-price">$29.99</div>
            </div>
            
            <!-- 商品2 -->
            <div class="product-item" data-asin="B09KLMN456" data-component-type="s-search-result">
                <div class="s-product-image-container">
                    <img class="s-image" src="https://via.placeholder.com/200x200/2196F3/white?text=Watering+Can" alt="Watering Can">
                </div>
                <h3 class="product-title">
                    <a href="/dp/B09KLMN456">Large Capacity Watering Can with Long Spout - 2 Gallon</a>
                </h3>
                <div class="product-rating">
                    <span class="stars">★★★★★</span>
                    <span>4.8 (1,567)</span>
                </div>
                <div class="product-price">$24.95</div>
            </div>
            
            <!-- 商品3 -->
            <div class="product-item" data-asin="B07PQRS789" data-component-type="s-search-result">
                <div class="s-product-image-container">
                    <img class="s-image" src="https://via.placeholder.com/200x200/FF9800/white?text=Pruning+Shears" alt="Pruning Shears">
                </div>
                <h3 class="product-title">
                    <a href="/dp/B07PQRS789">Premium Pruning Shears - Sharp Titanium Coated Blades</a>
                </h3>
                <div class="product-rating">
                    <span class="stars">★★★★☆</span>
                    <span>4.3 (892)</span>
                </div>
                <div class="product-price">$19.99</div>
            </div>
            
            <!-- 商品4 -->
            <div class="product-item" data-asin="B06TUVW012" data-component-type="s-search-result">
                <div class="s-product-image-container">
                    <img class="s-image" src="https://via.placeholder.com/200x200/9C27B0/white?text=Garden+Hose" alt="Garden Hose">
                </div>
                <h3 class="product-title">
                    <a href="/dp/B06TUVW012">Expandable Garden Hose 100ft - Lightweight & Durable</a>
                </h3>
                <div class="product-rating">
                    <span class="stars">★★★★☆</span>
                    <span>4.2 (3,456)</span>
                </div>
                <div class="product-price">$39.99</div>
            </div>
            
            <!-- 商品5 -->
            <div class="product-item" data-asin="B05XYZA345" data-component-type="s-search-result">
                <div class="s-product-image-container">
                    <img class="s-image" src="https://via.placeholder.com/200x200/795548/white?text=Soil+Tester" alt="Soil Tester">
                </div>
                <h3 class="product-title">
                    <a href="/dp/B05XYZA345">3-in-1 Soil Tester - pH, Moisture, Light Meter</a>
                </h3>
                <div class="product-rating">
                    <span class="stars">★★★★☆</span>
                    <span>4.1 (1,234)</span>
                </div>
                <div class="product-price">$12.99</div>
            </div>
            
            <!-- 商品6 -->
            <div class="product-item" data-asin="B04BCDE678" data-component-type="s-search-result">
                <div class="s-product-image-container">
                    <img class="s-image" src="https://via.placeholder.com/200x200/607D8B/white?text=Garden+Gloves" alt="Garden Gloves">
                </div>
                <h3 class="product-title">
                    <a href="/dp/B04BCDE678">Waterproof Garden Gloves - Thorn Resistant, Size Large</a>
                </h3>
                <div class="product-rating">
                    <span class="stars">★★★★★</span>
                    <span>4.7 (2,890)</span>
                </div>
                <div class="product-price">$15.99</div>
            </div>
        </div>
    </div>
    
    <script>
        // 模拟Amazon搜索页面的URL
        if (!window.location.href.includes('amazon.com/s')) {
            // 使用pushState来修改URL，模拟Amazon搜索页面
            const newUrl = window.location.origin + window.location.pathname + '?amazon.com/s?i=garden&rh=n%3A1063308&fs=true&page=2&qid=1750308943&xpid=NwSmqtuW1hkRb&ref=sr_pg_1';
            window.history.pushState({}, '', newUrl);
        }

        // 添加一些交互效果
        document.querySelectorAll('.product-item').forEach(item => {
            item.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-2px)';
            });

            item.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
            });
        });

        console.log('Amazon搜索页面测试页面已加载');
        console.log('商品数量:', document.querySelectorAll('[data-asin]').length);
        console.log('页面URL:', window.location.href);

        // 自动运行Amazon采集器测试
        setTimeout(() => {
            runAmazonCollectorTest();
        }, 1000);

        // Amazon采集器功能测试脚本
        function runAmazonCollectorTest() {
            console.log('🧪 开始测试Amazon采集器功能...');

            // 创建批量采集按钮（右上角红色）
            function createBatchCollectionButton() {
                // 移除已存在的按钮
                const existingButton = document.querySelector('.amazon-batch-collect-btn');
                if (existingButton) {
                    existingButton.remove();
                }

                const button = document.createElement('button');
                button.className = 'amazon-batch-collect-btn';
                button.type = 'button';

                // 获取匹配的商品数量
                const productCount = document.querySelectorAll('[data-asin]:not(.amazon-collected)').length;

                button.style.cssText = `
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    z-index: 9999;
                    padding: 8px 16px;
                    border-radius: 6px;
                    border: none;
                    background: #ff4d4f;
                    color: white;
                    cursor: pointer;
                    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                    display: flex;
                    align-items: center;
                    gap: 8px;
                    font-size: 14px;
                    font-weight: 500;
                    transition: all 0.3s ease;
                    font-family: Arial, sans-serif;
                `;

                // 创建图标
                const icon = document.createElement('div');
                icon.innerHTML = `
                    <svg width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                        <path d="M8 0a8 8 0 1 1 0 16A8 8 0 0 1 8 0zM4.5 7.5a.5.5 0 0 0 0 1h5.793l-2.147 2.146a.5.5 0 0 0 .708.708l3-3a.5.5 0 0 0 0-.708l-3-3a.5.5 0 1 0-.708.708L10.293 7.5H4.5z"/>
                    </svg>
                `;
                button.appendChild(icon);

                // 创建文字和数量
                const textContainer = document.createElement('div');
                textContainer.style.cssText = `
                    display: flex;
                    flex-direction: column;
                    align-items: flex-start;
                    line-height: 1.2;
                `;

                const mainText = document.createElement('span');
                mainText.textContent = '批量采集';
                mainText.style.fontSize = '14px';

                const countText = document.createElement('span');
                countText.className = 'product-count';
                countText.textContent = `${productCount} 个商品`;
                countText.style.cssText = `
                    font-size: 12px;
                    opacity: 0.9;
                    font-weight: normal;
                `;

                textContainer.appendChild(mainText);
                textContainer.appendChild(countText);
                button.appendChild(textContainer);

                // 添加点击事件
                button.addEventListener('click', () => {
                    alert(`开始批量采集 ${productCount} 个商品！`);
                    console.log('🚀 批量采集功能已触发');
                });

                // 添加悬停效果
                button.addEventListener('mouseenter', () => {
                    button.style.transform = 'scale(1.05)';
                    button.style.boxShadow = '0 4px 12px rgba(255, 77, 79, 0.3)';
                });

                button.addEventListener('mouseleave', () => {
                    button.style.transform = 'scale(1)';
                    button.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.15)';
                });

                document.body.appendChild(button);
                console.log('✅ 批量采集按钮已创建，位置：右上角');
                return button;
            }

            // 为商品图片添加单品采集按钮
            function addProductCollectionButtons() {
                const productElements = document.querySelectorAll('[data-asin]');
                let addedCount = 0;

                productElements.forEach(productElement => {
                    // 避免重复添加
                    if (productElement.querySelector('.amazon-product-collect-btn')) {
                        return;
                    }

                    // 查找商品图片容器
                    const imageContainer = productElement.querySelector('.s-product-image-container, .s-image-container');
                    if (imageContainer) {
                        // 设置图片容器为相对定位
                        const container = imageContainer;
                        container.style.position = 'relative';

                        // 创建单品采集按钮（固定显示）
                        const collectButton = document.createElement('div');
                        collectButton.className = 'amazon-product-collect-btn';
                        collectButton.style.cssText = `
                            position: absolute;
                            top: 8px;
                            right: 8px;
                            background: #ff4d4f;
                            color: white;
                            padding: 4px 8px;
                            border-radius: 4px;
                            font-size: 12px;
                            font-weight: 500;
                            cursor: pointer;
                            transition: all 0.3s ease;
                            user-select: none;
                            z-index: 10;
                            opacity: 1;
                            transform: translateY(0);
                            box-shadow: 0 2px 6px rgba(255, 77, 79, 0.3);
                            border: 1px solid rgba(255, 255, 255, 0.2);
                            font-family: Arial, sans-serif;
                        `;
                        collectButton.textContent = '单品采集';

                        // 添加悬停效果
                        collectButton.addEventListener('mouseenter', () => {
                            collectButton.style.background = '#ff7875';
                            collectButton.style.transform = 'scale(1.05)';
                            collectButton.style.boxShadow = '0 4px 12px rgba(255, 77, 79, 0.4)';
                        });

                        collectButton.addEventListener('mouseleave', () => {
                            collectButton.style.background = '#ff4d4f';
                            collectButton.style.transform = 'scale(1)';
                            collectButton.style.boxShadow = '0 2px 6px rgba(255, 77, 79, 0.3)';
                        });

                        // 添加点击事件
                        collectButton.addEventListener('click', (e) => {
                            e.preventDefault();
                            e.stopPropagation();
                            const asin = productElement.getAttribute('data-asin');
                            alert(`开始采集商品 ASIN: ${asin}`);
                            console.log('🎯 单品采集功能已触发，ASIN:', asin);
                        });

                        container.appendChild(collectButton);

                        addedCount++;
                    }
                });

                console.log(`✅ 已为 ${addedCount} 个商品添加单品采集按钮`);
                return addedCount;
            }

            console.log('🔧 创建批量采集按钮...');
            createBatchCollectionButton();

            console.log('🔧 添加单品采集按钮...');
            const buttonCount = addProductCollectionButtons();

            console.log('✅ Amazon采集器功能测试完成！');
            console.log(`📊 测试结果：`);
            console.log(`   - 批量采集按钮：已创建（右上角红色按钮）`);
            console.log(`   - 单品采集按钮：已为 ${buttonCount} 个商品添加（固定显示在图片右上角）`);
            console.log(`   - 使用方法：直接点击商品图片右上角的红色"单品采集"按钮`);

            // 显示成功通知
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 80px;
                right: 20px;
                z-index: 10000;
                padding: 12px 16px;
                border-radius: 6px;
                background: #52c41a;
                color: white;
                font-size: 14px;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                font-family: Arial, sans-serif;
            `;
            notification.textContent = '✅ Amazon采集器功能测试完成！';
            document.body.appendChild(notification);

            setTimeout(() => {
                notification.remove();
            }, 5000);
        }
    </script>
</body>
</html>
