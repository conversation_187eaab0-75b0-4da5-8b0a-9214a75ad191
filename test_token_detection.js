// 店小秘Token检测测试脚本

// 模拟Token检测服务（简化版）
class TokenDetectionTest {
    constructor() {
        this.STORAGE_KEY = 'dianxiaomi_token_status';
        this.SESSION_STORAGE_KEY = 'dianxiaomi_token_session';
        this.CACHE_DURATION = 5 * 60 * 1000; // 5分钟
        this.init();
    }
    
    init() {
        this.log('Token检测测试工具已初始化');
        this.checkToken(false);
    }
    
    async checkToken(forceRefresh = false) {
        try {
            this.log(`开始检查Token状态 (强制刷新: ${forceRefresh})`);
            
            // 检查缓存
            if (!forceRefresh) {
                const cached = this.getCachedStatus();
                if (cached && !this.isCacheExpired(cached)) {
                    this.log('使用缓存的Token状态');
                    this.updateUI(cached);
                    return cached;
                }
            }
            
            // 检查Cookie
            const cookieStatus = this.checkCookies();
            this.log(`Cookie检查结果: ${cookieStatus.hasDxmCookies ? '发现店小秘Cookie' : '未发现店小秘Cookie'}`);
            
            if (!cookieStatus.hasDxmCookies) {
                const status = {
                    isValid: false,
                    isLoggedIn: false,
                    message: '未检测到店小秘认证信息，请先登录店小秘网站',
                    lastCheckTime: Date.now()
                };
                this.saveStatus(status);
                this.updateUI(status);
                return status;
            }
            
            // API验证
            const apiResult = await this.verifyWithAPI();
            const status = {
                isValid: apiResult.success,
                isLoggedIn: apiResult.success,
                message: apiResult.message,
                userInfo: apiResult.userInfo,
                shopCount: apiResult.shopCount,
                lastCheckTime: Date.now(),
                error: apiResult.error
            };
            
            this.saveStatus(status);
            this.updateUI(status);
            return status;
            
        } catch (error) {
            this.log(`Token检测失败: ${error.message}`, 'error');
            const errorStatus = {
                isValid: false,
                isLoggedIn: false,
                message: `检测失败: ${error.message}`,
                lastCheckTime: Date.now(),
                error: error.message
            };
            this.updateUI(errorStatus);
            return errorStatus;
        }
    }
    
    checkCookies() {
        const cookies = this.getCookieDetails();
        const dxmCookieKeys = Object.keys(cookies).filter(key =>
            key.includes('dxm_') || 
            key.includes('JSESSIONID') || 
            key.includes('MYJ_') ||
            key.includes('dianxiaomi')
        );
        
        return {
            hasDxmCookies: dxmCookieKeys.length > 0,
            cookieDetails: cookies,
            dxmCookieKeys
        };
    }
    
    async verifyWithAPI() {
        try {
            this.log('调用店小秘用户信息API...');
            
            const response = await fetch('https://www.dianxiaomi.com/api/userInfo.json', {
                method: 'GET',
                credentials: 'include',
                headers: {
                    'Accept': 'application/json, text/plain, */*',
                    'X-Requested-With': 'XMLHttpRequest',
                    'User-Agent': navigator.userAgent
                }
            });
            
            this.log(`API响应状态: ${response.status}`);
            
            if (!response.ok) {
                if (response.status === 401 || response.status === 403) {
                    return {
                        success: false,
                        message: '店小秘未登录，请先登录',
                        error: 'Unauthorized'
                    };
                }
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const data = await response.json();
            this.log(`API响应数据: ${JSON.stringify(data, null, 2)}`);
            
            if (data.code === 0 && data.data) {
                const userInfo = data.data;
                let shopCount = 0;
                if (userInfo.shopMap) {
                    shopCount = Object.values(userInfo.shopMap).filter(shop => 
                        shop.platform === 'pddkj'
                    ).length;
                }
                
                return {
                    success: true,
                    message: `店小秘已登录 - 用户: ${userInfo.username || userInfo.name || '未知'}${shopCount > 0 ? `, Temu店铺: ${shopCount}个` : ''}`,
                    userInfo: {
                        id: userInfo.id,
                        username: userInfo.username,
                        name: userInfo.name,
                        phone: userInfo.phone,
                        vipLevel: userInfo.vipLevel
                    },
                    shopCount
                };
            } else {
                return {
                    success: false,
                    message: '店小秘认证无效，请重新登录',
                    error: data.msg || 'Invalid response'
                };
            }
            
        } catch (error) {
            this.log(`API验证失败: ${error.message}`, 'error');
            return {
                success: false,
                message: `API验证失败: ${error.message}`,
                error: error.message
            };
        }
    }
    
    getCookieDetails() {
        const cookies = document.cookie.split(';');
        const cookieObj = {};
        
        cookies.forEach(cookie => {
            const [name, value] = cookie.trim().split('=');
            if (name) {
                cookieObj[name] = value || '';
            }
        });
        
        return cookieObj;
    }
    
    getCachedStatus() {
        try {
            // 优先从sessionStorage获取
            let cached = sessionStorage.getItem(this.SESSION_STORAGE_KEY);
            if (cached) {
                return JSON.parse(cached);
            }
            
            // 降级到localStorage
            cached = localStorage.getItem(this.STORAGE_KEY);
            if (cached) {
                return JSON.parse(cached);
            }
            
            return null;
        } catch (error) {
            this.log(`获取缓存状态失败: ${error.message}`, 'error');
            return null;
        }
    }
    
    saveStatus(status) {
        try {
            localStorage.setItem(this.STORAGE_KEY, JSON.stringify(status));
            sessionStorage.setItem(this.SESSION_STORAGE_KEY, JSON.stringify(status));
            this.log('Token状态已保存到缓存');
        } catch (error) {
            this.log(`保存状态失败: ${error.message}`, 'error');
        }
    }
    
    isCacheExpired(status) {
        return Date.now() - status.lastCheckTime > this.CACHE_DURATION;
    }
    
    clearCache() {
        try {
            localStorage.removeItem(this.STORAGE_KEY);
            sessionStorage.removeItem(this.SESSION_STORAGE_KEY);
            this.log('缓存已清除');
            return true;
        } catch (error) {
            this.log(`清除缓存失败: ${error.message}`, 'error');
            return false;
        }
    }
    
    getCacheInfo() {
        const now = Date.now();
        const cached = this.getCachedStatus();
        const cacheAge = cached ? now - cached.lastCheckTime : 0;
        const isExpired = cached ? this.isCacheExpired(cached) : true;
        
        return {
            hasLocalStorage: !!localStorage.getItem(this.STORAGE_KEY),
            hasSessionStorage: !!sessionStorage.getItem(this.SESSION_STORAGE_KEY),
            cacheAge,
            isExpired,
            lastCheckTime: cached ? cached.lastCheckTime : 0
        };
    }
    
    updateUI(status) {
        const statusCard = document.getElementById('tokenStatusCard');
        const statusDiv = document.getElementById('tokenStatus');
        const infoDiv = document.getElementById('tokenInfo');
        
        if (!statusCard || !statusDiv || !infoDiv) return;
        
        // 更新状态卡片样式
        statusCard.className = 'status-card ' + (status.isValid ? 'status-valid' : 'status-invalid');
        
        // 更新状态文本
        statusDiv.innerHTML = `
            <div style="font-size: 16px; font-weight: 600; margin-bottom: 8px;">
                ${status.isValid ? '✅' : '❌'} ${status.message}
            </div>
        `;
        
        // 更新详细信息
        if (status.userInfo || status.shopCount !== undefined) {
            infoDiv.style.display = 'grid';
            infoDiv.innerHTML = `
                ${status.userInfo ? `
                    <div class="info-item">
                        <div class="info-label">用户名</div>
                        <div class="info-value">${status.userInfo.username || '未知'}</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">姓名</div>
                        <div class="info-value">${status.userInfo.name || '未设置'}</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">VIP等级</div>
                        <div class="info-value">${status.userInfo.vipLevel || 0}</div>
                    </div>
                ` : ''}
                ${status.shopCount !== undefined ? `
                    <div class="info-item">
                        <div class="info-label">Temu店铺数</div>
                        <div class="info-value">${status.shopCount}</div>
                    </div>
                ` : ''}
                <div class="info-item">
                    <div class="info-label">检查时间</div>
                    <div class="info-value">${new Date(status.lastCheckTime).toLocaleString()}</div>
                </div>
            `;
        } else {
            infoDiv.style.display = 'none';
        }
    }
    
    log(message, level = 'info') {
        const logArea = document.getElementById('logArea');
        if (!logArea) return;
        
        const timestamp = new Date().toLocaleTimeString();
        const levelIcon = level === 'error' ? '❌' : level === 'warn' ? '⚠️' : 'ℹ️';
        
        logArea.textContent += `[${timestamp}] ${levelIcon} ${message}\n`;
        logArea.scrollTop = logArea.scrollHeight;
        
        console.log(`[TokenTest] ${message}`);
    }
}

// 全局实例
let tokenTest;

// 全局方法
async function checkToken(forceRefresh = false) {
    if (tokenTest) {
        await tokenTest.checkToken(forceRefresh);
    }
}

function showCookieDetails() {
    if (!tokenTest) return;
    
    const cookieStatus = tokenTest.checkCookies();
    const cookieInfo = document.getElementById('cookieInfo');
    
    if (!cookieInfo) return;
    
    if (cookieStatus.hasDxmCookies) {
        const cookieList = Object.entries(cookieStatus.cookieDetails)
            .filter(([key]) => cookieStatus.dxmCookieKeys.includes(key))
            .map(([key, value]) => `
                <div class="cookie-item">
                    <strong>${key}:</strong> ${value.substring(0, 50)}${value.length > 50 ? '...' : ''}
                </div>
            `).join('');
        
        cookieInfo.innerHTML = `
            <div style="margin-bottom: 12px; font-weight: 600;">
                发现 ${cookieStatus.dxmCookieKeys.length} 个店小秘相关Cookie:
            </div>
            ${cookieList}
        `;
    } else {
        cookieInfo.innerHTML = '<div style="color: #ff4d4f;">未发现店小秘相关Cookie</div>';
    }
    
    tokenTest.log(`Cookie详情已更新，发现${cookieStatus.dxmCookieKeys.length}个相关Cookie`);
}

function showCacheInfo() {
    if (!tokenTest) return;
    
    const cacheInfo = tokenTest.getCacheInfo();
    const cacheInfoDiv = document.getElementById('cacheInfo');
    
    if (!cacheInfoDiv) return;
    
    cacheInfoDiv.innerHTML = `
        <div class="info-item">
            <div class="info-label">LocalStorage</div>
            <div class="info-value">${cacheInfo.hasLocalStorage ? '✅ 有缓存' : '❌ 无缓存'}</div>
        </div>
        <div class="info-item">
            <div class="info-label">SessionStorage</div>
            <div class="info-value">${cacheInfo.hasSessionStorage ? '✅ 有缓存' : '❌ 无缓存'}</div>
        </div>
        <div class="info-item">
            <div class="info-label">缓存年龄</div>
            <div class="info-value">${Math.round(cacheInfo.cacheAge / 1000)}秒</div>
        </div>
        <div class="info-item">
            <div class="info-label">是否过期</div>
            <div class="info-value">${cacheInfo.isExpired ? '❌ 已过期' : '✅ 有效'}</div>
        </div>
        <div class="info-item">
            <div class="info-label">最后检查</div>
            <div class="info-value">${cacheInfo.lastCheckTime ? new Date(cacheInfo.lastCheckTime).toLocaleString() : '从未检查'}</div>
        </div>
    `;
    
    tokenTest.log('缓存信息已更新');
}

function clearCache() {
    if (!tokenTest) return;
    
    if (tokenTest.clearCache()) {
        const cacheInfoDiv = document.getElementById('cacheInfo');
        if (cacheInfoDiv) {
            cacheInfoDiv.innerHTML = '<div style="color: #52c41a;">缓存已清除</div>';
        }
        // 重新检查状态
        checkToken(true);
    }
}

async function testAPI() {
    if (!tokenTest) return;
    
    tokenTest.log('开始测试API调用...');
    const result = await tokenTest.verifyWithAPI();
    tokenTest.log(`API测试结果: ${JSON.stringify(result, null, 2)}`);
}

function clearLog() {
    const logArea = document.getElementById('logArea');
    if (logArea) {
        logArea.textContent = '';
    }
}

function testInferLogin() {
    if (!tokenTest) return;

    tokenTest.log('测试智能推断登录状态...');

    // 模拟API成功的情况
    const inferredStatus = {
        isValid: true,
        isLoggedIn: true,
        message: '店小秘已登录（智能推断测试）',
        userInfo: {
            id: 999,
            username: '测试用户',
            name: '智能推断',
            phone: '138****8888',
            vipLevel: 1
        },
        shopCount: 2,
        lastCheckTime: Date.now()
    };

    tokenTest.saveStatus(inferredStatus);
    tokenTest.updateUI(inferredStatus);
    tokenTest.log('智能推断测试完成，状态已更新');
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    tokenTest = new TokenDetectionTest();
    
    // 绑定事件监听器
    const bindEvent = (id, handler) => {
        const element = document.getElementById(id);
        if (element) {
            element.addEventListener('click', handler);
        }
    };
    
    bindEvent('checkTokenBtn', () => checkToken(false));
    bindEvent('refreshTokenBtn', () => checkToken(true));
    bindEvent('showCookieBtn', showCookieDetails);
    bindEvent('showCacheBtn', showCacheInfo);
    bindEvent('clearCacheBtn', clearCache);
    bindEvent('testAPIBtn', testAPI);
    bindEvent('clearLogBtn', clearLog);

    // 添加智能推断测试按钮
    const testInferBtn = document.createElement('button');
    testInferBtn.className = 'btn btn-success';
    testInferBtn.textContent = '🧠 测试智能推断';
    testInferBtn.onclick = testInferLogin;

    const operationPanel = document.querySelector('.container:nth-child(2)');
    if (operationPanel) {
        operationPanel.appendChild(testInferBtn);
    }
});
});
