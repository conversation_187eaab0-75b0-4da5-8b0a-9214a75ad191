// Background JSZip测试脚本

class BackgroundJSZipTester {
    constructor() {
        this.init();
    }

    init() {
        this.log('Background JSZip测试工具已初始化');
        this.checkBackgroundStatus();
    }

    checkBackgroundStatus() {
        const backgroundStatus = document.getElementById('backgroundStatus');
        const backgroundInfo = document.getElementById('backgroundInfo');
        
        try {
            // 检查扩展环境
            if (typeof chrome !== 'undefined' && chrome.runtime) {
                this.log('✅ 扩展环境可用');
                backgroundStatus.className = 'status-card status-valid';
                backgroundInfo.innerHTML = `
                    <div style="color: #52c41a;">
                        ✅ 扩展环境检测成功<br>
                        扩展ID: ${chrome.runtime.id}<br>
                        可以与background.js通信<br>
                        点击"测试Background JSZip"验证JSZip库
                    </div>
                `;
            } else {
                throw new Error('扩展环境不可用');
            }
        } catch (error) {
            this.log('❌ 扩展环境检查失败: ' + error.message, 'error');
            backgroundStatus.className = 'status-card status-invalid';
            backgroundInfo.innerHTML = `
                <div style="color: #ff4d4f;">
                    ❌ 扩展环境检测失败<br>
                    错误: ${error.message}<br>
                    请确保在扩展环境中运行此页面
                </div>
            `;
        }
    }

    async testBackgroundJSZip() {
        try {
            this.log('开始测试Background中的JSZip库...');
            
            // 检查扩展环境
            if (typeof chrome === 'undefined' || !chrome.runtime) {
                throw new Error('扩展环境不可用');
            }
            
            this.log('✅ 扩展环境可用，发送测试消息到background...');
            
            // 发送测试消息到background
            const result = await new Promise((resolve, reject) => {
                chrome.runtime.sendMessage({
                    action: 'TEST_JSZIP_AVAILABILITY'
                }, (response) => {
                    if (chrome.runtime.lastError) {
                        reject(new Error(chrome.runtime.lastError.message));
                    } else {
                        resolve(response);
                    }
                });
            });
            
            if (result && result.success) {
                this.log('✅ Background JSZip测试成功');
                this.log('JSZip版本: ' + (result.version || '未知'));
                this.log('测试结果: ' + JSON.stringify(result, null, 2));
                
                // 更新状态显示
                const backgroundStatus = document.getElementById('backgroundStatus');
                const backgroundInfo = document.getElementById('backgroundInfo');
                
                backgroundStatus.className = 'status-card status-valid';
                backgroundInfo.innerHTML = `
                    <div style="color: #52c41a;">
                        ✅ Background JSZip测试成功<br>
                        JSZip版本: ${result.version || '未知'}<br>
                        库状态: 可用<br>
                        可以正常创建ZIP文件
                    </div>
                `;
            } else {
                throw new Error(result?.error || 'Background JSZip测试失败');
            }
            
        } catch (error) {
            this.log('❌ Background JSZip测试失败: ' + error.message, 'error');
            
            const backgroundStatus = document.getElementById('backgroundStatus');
            const backgroundInfo = document.getElementById('backgroundInfo');
            
            backgroundStatus.className = 'status-card status-invalid';
            backgroundInfo.innerHTML = `
                <div style="color: #ff4d4f;">
                    ❌ Background JSZip测试失败<br>
                    错误: ${error.message}<br>
                    请检查JSZip库是否正确导入到background.js
                </div>
            `;
        }
    }

    async createTestZip() {
        try {
            this.log('开始创建测试ZIP文件...');
            
            // 获取测试数据
            const testData = document.getElementById('testJsonData').value.trim();
            if (!testData) {
                throw new Error('请输入测试JSON数据');
            }
            
            this.log('测试数据: ' + testData);
            
            // 验证JSON格式
            try {
                JSON.parse(testData);
                this.log('✅ JSON格式验证通过');
            } catch (e) {
                throw new Error('JSON格式错误: ' + e.message);
            }
            
            // 发送ZIP创建请求到background
            this.log('发送ZIP创建请求到background...');
            
            const result = await new Promise((resolve, reject) => {
                chrome.runtime.sendMessage({
                    action: 'CREATE_ZIP_FILE',
                    jsonData: testData
                }, (response) => {
                    if (chrome.runtime.lastError) {
                        reject(new Error(chrome.runtime.lastError.message));
                    } else {
                        resolve(response);
                    }
                });
            });
            
            if (result && result.success) {
                this.log('✅ ZIP文件创建成功');
                this.log(`ZIP文件大小: ${result.size} bytes`);
                
                // 如果有zipBlob，可以下载测试
                if (result.zipBlob) {
                    this.log('准备下载测试ZIP文件...');
                    
                    // 创建下载链接
                    const url = URL.createObjectURL(result.zipBlob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = 'test-choiceSave.zip';
                    a.click();
                    URL.revokeObjectURL(url);
                    
                    this.log('✅ 测试ZIP文件已下载');
                }
                
                alert('ZIP文件创建成功！大小: ' + result.size + ' bytes');
            } else {
                throw new Error(result?.error || 'ZIP文件创建失败');
            }
            
        } catch (error) {
            this.log('❌ 创建测试ZIP失败: ' + error.message, 'error');
            alert('创建测试ZIP失败: ' + error.message);
        }
    }

    log(message, level = 'info') {
        const logArea = document.getElementById('logArea');
        const timestamp = new Date().toLocaleTimeString();
        const levelIcon = level === 'error' ? '❌' : level === 'warn' ? '⚠️' : 'ℹ️';
        
        logArea.textContent += `[${timestamp}] ${levelIcon} ${message}\n`;
        logArea.scrollTop = logArea.scrollHeight;
        
        console.log(`[BackgroundJSZipTester] ${message}`);
    }

    clearLog() {
        document.getElementById('logArea').textContent = '';
    }
}

// 全局实例
let backgroundJSZipTester;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    backgroundJSZipTester = new BackgroundJSZipTester();
    
    // 绑定事件
    document.getElementById('testBackgroundJSZipBtn').addEventListener('click', () => {
        backgroundJSZipTester.testBackgroundJSZip();
    });
    
    document.getElementById('createZipBtn').addEventListener('click', () => {
        backgroundJSZipTester.createTestZip();
    });
    
    document.getElementById('clearLogBtn').addEventListener('click', () => {
        backgroundJSZipTester.clearLog();
    });
});
