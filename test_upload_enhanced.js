// 增强版上传测试工具JavaScript代码

// 增强版上传测试工具
class EnhancedUploadTester {
    constructor() {
        this.isExtensionAvailable = false;
        this.shopList = [];
        this.init();
    }

    async init() {
        this.log('增强版上传测试工具已初始化');
        await this.checkExtensionStatus();
        await this.checkConnectionStatus();
    }

    // 检查扩展状态
    async checkExtensionStatus() {
        try {
            if (typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.sendMessage) {
                this.isExtensionAvailable = true;
                this.log('✅ 扩展环境可用');
                
                const extensionInfo = document.getElementById('extensionInfo');
                extensionInfo.innerHTML = `
                    <div style="color: #52c41a;">
                        ✅ 扩展环境检测成功<br>
                        扩展ID: ${chrome.runtime.id}<br>
                        可以通过background.js调用店小秘API
                    </div>
                `;
                
                document.getElementById('extensionStatus').className = 'status-card status-valid';
            } else {
                throw new Error('扩展环境不可用');
            }
        } catch (error) {
            this.isExtensionAvailable = false;
            this.log('❌ 扩展环境不可用: ' + error.message, 'error');
            
            const extensionInfo = document.getElementById('extensionInfo');
            extensionInfo.innerHTML = `
                <div style="color: #ff4d4f;">
                    ❌ 扩展环境检测失败<br>
                    错误: ${error.message}<br>
                    请确保此页面在扩展环境中运行
                </div>
            `;
            
            document.getElementById('extensionStatus').className = 'status-card status-invalid';
        }
    }

    // 检查连接状态
    async checkConnectionStatus() {
        this.log('开始检查连接状态...');
        
        const connectionCard = document.getElementById('connectionStatusCard');
        const connectionStatus = document.getElementById('connectionStatus');
        
        if (!this.isExtensionAvailable) {
            connectionCard.className = 'status-card status-invalid';
            connectionStatus.innerHTML = '❌ 扩展环境不可用，无法检查连接状态';
            
            document.getElementById('extensionConnection').textContent = '❌ 不可用';
            document.getElementById('dianxiaomiTab').textContent = '❌ 无法检查';
            document.getElementById('tokenStatus').textContent = '❌ 无法检查';
            document.getElementById('userInfo').textContent = '❌ 无法检查';
            return;
        }

        try {
            // 检查扩展连接
            document.getElementById('extensionConnection').textContent = '✅ 已连接';
            
            // 获取Token状态
            const tokenResult = await this.sendMessageToBackground({
                action: 'GET_DIANXIAOMI_TOKEN_STATUS'
            });
            
            if (tokenResult.success) {
                connectionCard.className = 'status-card status-valid';
                connectionStatus.innerHTML = '✅ 连接正常，可以调用店小秘API';
                
                document.getElementById('dianxiaomiTab').textContent = '✅ 已找到';
                document.getElementById('tokenStatus').textContent = '✅ 有效';
                
                if (tokenResult.data && tokenResult.data.data) {
                    const userInfo = tokenResult.data.data;
                    document.getElementById('userInfo').textContent = 
                        `${userInfo.account || userInfo.username || '未知用户'}`;
                } else {
                    document.getElementById('userInfo').textContent = '✅ 已登录';
                }
                
                this.log('✅ 连接状态检查成功');
            } else {
                throw new Error(tokenResult.error || '获取Token状态失败');
            }
        } catch (error) {
            this.log('❌ 连接状态检查失败: ' + error.message, 'error');
            
            connectionCard.className = 'status-card status-invalid';
            connectionStatus.innerHTML = `❌ 连接失败: ${error.message}`;
            
            document.getElementById('dianxiaomiTab').textContent = '❌ 未找到';
            document.getElementById('tokenStatus').textContent = '❌ 无效';
            document.getElementById('userInfo').textContent = '❌ 未登录';
        }
    }

    // 向background发送消息
    async sendMessageToBackground(message) {
        return new Promise((resolve, reject) => {
            if (!this.isExtensionAvailable) {
                reject(new Error('扩展环境不可用'));
                return;
            }

            chrome.runtime.sendMessage(message, (response) => {
                if (chrome.runtime.lastError) {
                    reject(new Error(chrome.runtime.lastError.message));
                } else {
                    resolve(response);
                }
            });
        });
    }

    // 获取Token状态
    async getTokenStatus() {
        try {
            this.log('获取Token状态...');
            
            const result = await this.sendMessageToBackground({
                action: 'GET_DIANXIAOMI_TOKEN_STATUS'
            });
            
            if (result.success) {
                this.log('✅ Token状态获取成功');
                this.log('Token数据: ' + JSON.stringify(result.data, null, 2));
            } else {
                this.log('❌ Token状态获取失败: ' + result.error, 'error');
            }
        } catch (error) {
            this.log('❌ 获取Token状态异常: ' + error.message, 'error');
        }
    }

    // 获取店铺列表
    async getShopList() {
        try {
            this.log('获取店铺列表...');
            
            const result = await this.sendMessageToBackground({
                action: 'GET_DIANXIAOMI_SHOP_LIST'
            });
            
            if (result.success) {
                this.log('✅ 店铺列表获取成功');
                this.log('店铺数据: ' + JSON.stringify(result.data, null, 2));
                
                // 更新店铺信息显示
                this.updateShopInfo(result.data);
                
                // 更新店铺选择下拉框
                this.updateShopSelect(result.data);
            } else {
                this.log('❌ 店铺列表获取失败: ' + result.error, 'error');
            }
        } catch (error) {
            this.log('❌ 获取店铺列表异常: ' + error.message, 'error');
        }
    }

    // 更新店铺信息显示
    updateShopInfo(shopData) {
        const shopInfo = document.getElementById('shopInfo');
        
        if (shopData && Array.isArray(shopData)) {
            this.shopList = shopData;
            
            shopInfo.innerHTML = shopData.map(shop => `
                <div class="info-item">
                    <div class="info-label">${shop.shopName || '未知店铺'}</div>
                    <div class="info-value">
                        ID: ${shop.shopId || 'N/A'}<br>
                        状态: ${shop.status || 'N/A'}<br>
                        平台: ${shop.platform || 'N/A'}
                    </div>
                </div>
            `).join('');
        } else {
            shopInfo.innerHTML = `
                <div class="info-item">
                    <div class="info-label">错误</div>
                    <div class="info-value">店铺数据格式不正确</div>
                </div>
            `;
        }
    }

    // 更新店铺选择下拉框
    updateShopSelect(shopData) {
        const shopSelect = document.getElementById('shopSelect');
        
        shopSelect.innerHTML = '<option value="">请选择店铺</option>';
        
        if (shopData && Array.isArray(shopData)) {
            shopData.forEach(shop => {
                const option = document.createElement('option');
                option.value = shop.shopId;
                option.textContent = `${shop.shopName} (${shop.shopId})`;
                shopSelect.appendChild(option);
            });
        }
    }

    // 测试API调用
    async testAPI() {
        try {
            this.log('测试API调用...');
            
            const result = await this.sendMessageToBackground({
                action: 'CALL_DIANXIAOMI_API',
                apiConfig: {
                    url: 'https://www.dianxiaomi.com/api/userInfo.json',
                    method: 'GET'
                }
            });
            
            if (result.success) {
                this.log('✅ API调用测试成功');
                this.log('API响应: ' + JSON.stringify(result.data, null, 2));
            } else {
                this.log('❌ API调用测试失败: ' + result.error, 'error');
            }
        } catch (error) {
            this.log('❌ API调用测试异常: ' + error.message, 'error');
        }
    }

    // 上传商品
    async uploadProduct(productData) {
        try {
            this.log('开始上传商品...');
            this.log('商品数据: ' + JSON.stringify(productData, null, 2));
            
            const result = await this.sendMessageToBackground({
                action: 'UPLOAD_PRODUCT',
                productData
            });
            
            if (result.success) {
                this.log('✅ 商品上传成功');
                this.log('上传结果: ' + JSON.stringify(result.data, null, 2));
                alert('商品上传成功！');
            } else {
                this.log('❌ 商品上传失败: ' + result.error, 'error');
                alert('商品上传失败: ' + result.error);
            }
        } catch (error) {
            this.log('❌ 商品上传异常: ' + error.message, 'error');
            alert('商品上传异常: ' + error.message);
        }
    }

    // 日志记录
    log(message, level = 'info') {
        const logArea = document.getElementById('logArea');
        const timestamp = new Date().toLocaleTimeString();
        const levelIcon = level === 'error' ? '❌' : level === 'warn' ? '⚠️' : 'ℹ️';
        
        logArea.textContent += `[${timestamp}] ${levelIcon} ${message}\n`;
        logArea.scrollTop = logArea.scrollHeight;
        
        console.log(`[EnhancedUploadTester] ${message}`);
    }

    // 清除日志
    clearLog() {
        document.getElementById('logArea').textContent = '';
    }
}

// 全局实例
let uploadTester;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    uploadTester = new EnhancedUploadTester();
    
    // 绑定事件
    document.getElementById('checkConnectionBtn').addEventListener('click', () => {
        uploadTester.checkConnectionStatus();
    });
    
    document.getElementById('getTokenStatusBtn').addEventListener('click', () => {
        uploadTester.getTokenStatus();
    });
    
    document.getElementById('getShopListBtn').addEventListener('click', () => {
        uploadTester.getShopList();
    });
    
    document.getElementById('testAPIBtn').addEventListener('click', () => {
        uploadTester.testAPI();
    });
    
    document.getElementById('clearLogBtn').addEventListener('click', () => {
        uploadTester.clearLog();
    });
    
    // 表单提交事件
    document.getElementById('uploadForm').addEventListener('submit', (e) => {
        e.preventDefault();
        
        const formData = new FormData(e.target);
        const productData = {
            shopId: document.getElementById('shopSelect').value,
            title: document.getElementById('productTitle').value,
            description: document.getElementById('productDescription').value,
            price: parseFloat(document.getElementById('productPrice').value),
            stock: parseInt(document.getElementById('productStock').value)
        };
        
        if (!productData.shopId) {
            alert('请先选择店铺');
            return;
        }
        
        uploadTester.uploadProduct(productData);
    });
});
