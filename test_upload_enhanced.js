// 增强版上传测试工具JavaScript代码
// 完全借鉴test_upload.html的验证和上传逻辑，通过扩展background.js实现

// 增强版上传测试工具
class EnhancedUploadTester {
    constructor() {
        this.isExtensionAvailable = false;
        this.shopList = [];
        this.init();
    }

    async init() {
        this.log('增强版上传测试工具已初始化');
        this.log('借鉴test_upload.html的完整验证和上传逻辑');
        await this.checkExtensionStatus();
        await this.checkDxmTokenStatus();
    }

    // 检查扩展状态
    async checkExtensionStatus() {
        try {
            if (typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.sendMessage) {
                this.isExtensionAvailable = true;
                this.log('✅ 扩展环境可用');
                
                const extensionInfo = document.getElementById('extensionInfo');
                extensionInfo.innerHTML = `
                    <div style="color: #52c41a;">
                        ✅ 扩展环境检测成功<br>
                        扩展ID: ${chrome.runtime.id}<br>
                        可以通过background.js调用店小秘API
                    </div>
                `;
                
                document.getElementById('extensionStatus').className = 'status-card status-valid';
            } else {
                throw new Error('扩展环境不可用');
            }
        } catch (error) {
            this.isExtensionAvailable = false;
            this.log('❌ 扩展环境不可用: ' + error.message, 'error');
            
            const extensionInfo = document.getElementById('extensionInfo');
            extensionInfo.innerHTML = `
                <div style="color: #ff4d4f;">
                    ❌ 扩展环境检测失败<br>
                    错误: ${error.message}<br>
                    请确保此页面在扩展环境中运行
                </div>
            `;
            
            document.getElementById('extensionStatus').className = 'status-card status-invalid';
        }
    }

    // 检查店小秘Token状态 - 借鉴test_upload.html的逻辑
    async checkDxmTokenStatus() {
        this.log('🔐 检查店小秘Token状态...');

        const connectionCard = document.getElementById('connectionStatusCard');
        const connectionStatus = document.getElementById('connectionStatus');

        if (!this.isExtensionAvailable) {
            connectionCard.className = 'status-card status-invalid';
            connectionStatus.innerHTML = '❌ 扩展环境不可用，无法检查Token状态';

            document.getElementById('extensionConnection').textContent = '❌ 不可用';
            document.getElementById('dianxiaomiTab').textContent = '❌ 无法检查';
            document.getElementById('tokenStatus').textContent = '❌ 无法检查';
            document.getElementById('userInfo').textContent = '❌ 无法检查';
            return false;
        }

        try {
            // 检查扩展连接
            document.getElementById('extensionConnection').textContent = '✅ 已连接';

            // 通过background.js调用店小秘用户信息API验证登录状态
            this.log('通过background.js验证店小秘登录状态...');
            const tokenResult = await this.sendMessageToBackground({
                action: 'GET_DIANXIAOMI_TOKEN_STATUS'
            });

            if (tokenResult.success) {
                this.log('✅ 店小秘API调用成功');
                this.log('API响应: ' + JSON.stringify(tokenResult.data, null, 2));

                if (tokenResult.data && tokenResult.data.code === 0 && tokenResult.data.data) {
                    const userInfo = tokenResult.data.data;

                    connectionCard.className = 'status-card status-valid';
                    connectionStatus.innerHTML = `✅ 店小秘已登录 - 用户: ${userInfo.account || userInfo.username || userInfo.name || '未知'}`;

                    document.getElementById('dianxiaomiTab').textContent = '✅ 已找到';
                    document.getElementById('tokenStatus').textContent = '✅ 有效';
                    document.getElementById('userInfo').textContent =
                        `${userInfo.account || userInfo.username || userInfo.name || '未知用户'}`;

                    this.log(`✅ 店小秘已登录 - 用户: ${userInfo.account || userInfo.username || userInfo.name || '未知'}`);
                    return true;
                } else {
                    throw new Error('店小秘认证无效，请重新登录');
                }
            } else {
                throw new Error(tokenResult.error || '获取Token状态失败');
            }
        } catch (error) {
            this.log('❌ Token状态检查失败: ' + error.message, 'error');

            connectionCard.className = 'status-card status-invalid';
            connectionStatus.innerHTML = `❌ 店小秘未登录或认证失效: ${error.message}`;

            document.getElementById('dianxiaomiTab').textContent = '❌ 未找到';
            document.getElementById('tokenStatus').textContent = '❌ 无效';
            document.getElementById('userInfo').textContent = '❌ 未登录';

            return false;
        }
    }

    // 向background发送消息
    async sendMessageToBackground(message) {
        return new Promise((resolve, reject) => {
            if (!this.isExtensionAvailable) {
                reject(new Error('扩展环境不可用'));
                return;
            }

            chrome.runtime.sendMessage(message, (response) => {
                if (chrome.runtime.lastError) {
                    reject(new Error(chrome.runtime.lastError.message));
                } else {
                    resolve(response);
                }
            });
        });
    }

    // 验证JSON - 借鉴test_upload.html
    validateJson(jsonData) {
        if (!jsonData || !jsonData.trim()) {
            this.log('❌ 请输入JSON数据', 'error');
            return false;
        }

        try {
            JSON.parse(jsonData);
            this.log('✅ JSON格式验证通过');
            return true;
        } catch (e) {
            this.log('❌ JSON格式错误：' + e.message, 'error');
            return false;
        }
    }

    // 检查JSZip库是否可用
    checkJSZipAvailability() {
        if (typeof JSZip === 'undefined') {
            this.log('❌ JSZip库未加载，需要在扩展环境中使用', 'error');
            return false;
        }
        this.log('✅ JSZip库已加载');
        return true;
    }

    // 注意：ZIP文件创建已移到content script中，因为background环境中JSZip不可用

    // 上传数据 - 完全借鉴test_upload.html的逻辑
    async uploadData(jsonData) {
        this.log('🚀 开始上传流程...');

        // 第一步：验证JSON
        if (!this.validateJson(jsonData)) {
            return;
        }

        // 第二步：检查店小秘登录状态
        this.log('🔐 检查店小秘登录状态...');
        const isLoggedIn = await this.checkDxmTokenStatus();

        if (!isLoggedIn) {
            this.log('❌ 店小秘未登录或认证失效，请先登录店小秘网站', 'error');
            return;
        }

        this.log('✅ 认证检查通过，开始上传流程...');

        try {
            // 第三步：通过background.js上传数据
            this.log('📤 通过background.js上传数据到店小秘...');

            const uploadResult = await this.sendMessageToBackground({
                action: 'UPLOAD_DIANXIAOMI_PRODUCT',
                jsonData: jsonData
            });

            if (uploadResult.success) {
                this.log('✅ 上传成功！');
                this.log('上传结果: ' + JSON.stringify(uploadResult.data, null, 2));

                // 显示响应结果
                this.showResponse(uploadResult);

                alert('商品上传成功！');
            } else {
                this.log('❌ 上传失败: ' + uploadResult.error, 'error');

                // 显示错误响应
                this.showResponse(uploadResult);

                alert('商品上传失败: ' + uploadResult.error);
            }

        } catch (error) {
            this.log('❌ 上传异常: ' + error.message, 'error');
            alert('商品上传异常: ' + error.message);
        }
    }

    // 显示响应结果 - 借鉴test_upload.html
    showResponse(result) {
        this.log('=== 店小秘API响应结果 ===');
        this.log('成功状态: ' + (result.success ? '✅ 成功' : '❌ 失败'));

        if (result.error) {
            this.log('错误信息: ' + result.error);
        }

        if (result.data) {
            this.log('响应数据: ' + JSON.stringify(result.data, null, 2));
        }

        if (result.status) {
            this.log('HTTP状态: ' + result.status);
        }

        if (result.headers) {
            this.log('响应头: ' + JSON.stringify(result.headers, null, 2));
        }

        this.log('响应时间: ' + new Date().toLocaleString());
        this.log('========================');
    }

    // 获取店铺列表
    async getShopList() {
        try {
            this.log('获取店铺列表...');
            
            const result = await this.sendMessageToBackground({
                action: 'GET_DIANXIAOMI_SHOP_LIST'
            });
            
            if (result.success) {
                this.log('✅ 店铺列表获取成功');
                this.log('店铺数据: ' + JSON.stringify(result.data, null, 2));
                
                // 更新店铺信息显示
                this.updateShopInfo(result.data);
                
                // 更新店铺选择下拉框
                this.updateShopSelect(result.data);
            } else {
                this.log('❌ 店铺列表获取失败: ' + result.error, 'error');
            }
        } catch (error) {
            this.log('❌ 获取店铺列表异常: ' + error.message, 'error');
        }
    }

    // 更新店铺信息显示
    updateShopInfo(shopData) {
        const shopInfo = document.getElementById('shopInfo');
        
        if (shopData && Array.isArray(shopData)) {
            this.shopList = shopData;
            
            shopInfo.innerHTML = shopData.map(shop => `
                <div class="info-item">
                    <div class="info-label">${shop.shopName || '未知店铺'}</div>
                    <div class="info-value">
                        ID: ${shop.shopId || 'N/A'}<br>
                        状态: ${shop.status || 'N/A'}<br>
                        平台: ${shop.platform || 'N/A'}
                    </div>
                </div>
            `).join('');
        } else {
            shopInfo.innerHTML = `
                <div class="info-item">
                    <div class="info-label">错误</div>
                    <div class="info-value">店铺数据格式不正确</div>
                </div>
            `;
        }
    }

    // 更新店铺选择下拉框
    updateShopSelect(shopData) {
        const shopSelect = document.getElementById('shopSelect');
        
        shopSelect.innerHTML = '<option value="">请选择店铺</option>';
        
        if (shopData && Array.isArray(shopData)) {
            shopData.forEach(shop => {
                const option = document.createElement('option');
                option.value = shop.shopId;
                option.textContent = `${shop.shopName} (${shop.shopId})`;
                shopSelect.appendChild(option);
            });
        }
    }

    // 测试API调用
    async testAPI() {
        try {
            this.log('测试API调用...');
            
            const result = await this.sendMessageToBackground({
                action: 'CALL_DIANXIAOMI_API',
                apiConfig: {
                    url: 'https://www.dianxiaomi.com/api/userInfo.json',
                    method: 'GET'
                }
            });
            
            if (result.success) {
                this.log('✅ API调用测试成功');
                this.log('API响应: ' + JSON.stringify(result.data, null, 2));
            } else {
                this.log('❌ API调用测试失败: ' + result.error, 'error');
            }
        } catch (error) {
            this.log('❌ API调用测试异常: ' + error.message, 'error');
        }
    }

    // 上传商品
    async uploadProduct(productData) {
        try {
            this.log('开始上传商品...');
            this.log('商品数据: ' + JSON.stringify(productData, null, 2));
            
            const result = await this.sendMessageToBackground({
                action: 'UPLOAD_PRODUCT',
                productData
            });
            
            if (result.success) {
                this.log('✅ 商品上传成功');
                this.log('上传结果: ' + JSON.stringify(result.data, null, 2));
                alert('商品上传成功！');
            } else {
                this.log('❌ 商品上传失败: ' + result.error, 'error');
                alert('商品上传失败: ' + result.error);
            }
        } catch (error) {
            this.log('❌ 商品上传异常: ' + error.message, 'error');
            alert('商品上传异常: ' + error.message);
        }
    }

    // 日志记录
    log(message, level = 'info') {
        const logArea = document.getElementById('logArea');
        const timestamp = new Date().toLocaleTimeString();
        const levelIcon = level === 'error' ? '❌' : level === 'warn' ? '⚠️' : 'ℹ️';
        
        logArea.textContent += `[${timestamp}] ${levelIcon} ${message}\n`;
        logArea.scrollTop = logArea.scrollHeight;
        
        console.log(`[EnhancedUploadTester] ${message}`);
    }

    // 加载示例数据 - 借鉴test_upload.html
    loadSampleData() {
        const sampleData = `{"attributes":"[{\\"propName\\":\\"是否可用于食品接触\\",\\"refPid\\":4010,\\"pid\\":1795,\\"templatePid\\":1261897,\\"numberInputValue\\":\\"\\",\\"valueUnit\\":\\"\\",\\"vid\\":\\"67313\\",\\"propValue\\":\\"是\\"}]","categoryId":"9938","shopId":"6959965","productSemiManagedReq":"100","sourceUrl":"https://www.amazon.com/dp/B0D2R2CGC1","productName":"3in1 Cup Lid Brush MultiFunction Gap Cleaning Brush","op":1}`;

        document.getElementById('jsonData').value = sampleData;
        this.log('✅ 已加载示例数据');
    }

    // 格式化JSON - 借鉴test_upload.html
    formatJson() {
        const textarea = document.getElementById('jsonData');
        try {
            const jsonObj = JSON.parse(textarea.value);
            textarea.value = JSON.stringify(jsonObj, null, 2);
            this.log('✅ JSON格式化成功');
        } catch (e) {
            this.log('❌ JSON格式错误：' + e.message, 'error');
        }
    }

    // 压缩JSON - 借鉴test_upload.html
    compressJson() {
        const textarea = document.getElementById('jsonData');
        try {
            const jsonObj = JSON.parse(textarea.value);
            textarea.value = JSON.stringify(jsonObj);
            this.log('✅ JSON压缩成功');
        } catch (e) {
            this.log('❌ JSON格式错误：' + e.message, 'error');
        }
    }

    // 验证JSON按钮处理
    validateJsonButton() {
        const jsonData = document.getElementById('jsonData').value.trim();
        this.validateJson(jsonData);
    }

    // 上传数据按钮处理
    async uploadDataButton() {
        const jsonData = document.getElementById('jsonData').value.trim();
        await this.uploadData(jsonData);
    }

    // 清除日志
    clearLog() {
        document.getElementById('logArea').textContent = '';
    }
}

// 全局实例
let uploadTester;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    uploadTester = new EnhancedUploadTester();

    // 绑定原有事件
    document.getElementById('checkConnectionBtn').addEventListener('click', () => {
        uploadTester.checkDxmTokenStatus();
    });

    document.getElementById('getTokenStatusBtn').addEventListener('click', () => {
        uploadTester.getTokenStatus();
    });

    document.getElementById('getShopListBtn').addEventListener('click', () => {
        uploadTester.getShopList();
    });

    document.getElementById('testAPIBtn').addEventListener('click', () => {
        uploadTester.testAPI();
    });

    document.getElementById('clearLogBtn').addEventListener('click', () => {
        uploadTester.clearLog();
    });

    // 绑定新的JSON处理事件 - 借鉴test_upload.html
    document.getElementById('formatJsonBtn').addEventListener('click', () => {
        uploadTester.formatJson();
    });

    document.getElementById('compressJsonBtn').addEventListener('click', () => {
        uploadTester.compressJson();
    });

    document.getElementById('loadSampleBtn').addEventListener('click', () => {
        uploadTester.loadSampleData();
    });

    document.getElementById('validateJsonBtn').addEventListener('click', () => {
        uploadTester.validateJsonButton();
    });

    document.getElementById('uploadDataBtn').addEventListener('click', () => {
        uploadTester.uploadDataButton();
    });

    // 键盘快捷键 - 借鉴test_upload.html
    document.addEventListener('keydown', function(e) {
        if (e.ctrlKey) {
            switch(e.key) {
                case 'Enter':
                    e.preventDefault();
                    uploadTester.uploadDataButton();
                    break;
                case 'l':
                    e.preventDefault();
                    uploadTester.loadSampleData();
                    break;
                case 'f':
                    e.preventDefault();
                    uploadTester.formatJson();
                    break;
            }
        }
    });
});
