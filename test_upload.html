<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>店小秘数据上传测试页面</title>
    <!-- 引入本地JSZip库用于创建ZIP文件 -->
    <script src="src/lib/jszip.min.js"></script>
    <!-- 引入主要的JavaScript文件 -->
    <script src="test_upload.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #555;
        }
        
        textarea {
            width: 100%;
            height: 300px;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            resize: vertical;
            box-sizing: border-box;
        }
        
        textarea:focus {
            border-color: #4CAF50;
            outline: none;
        }
        
        .button-group {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-top: 30px;
        }
        
        button {
            padding: 12px 30px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background-color: #4CAF50;
            color: white;
        }
        
        .btn-primary:hover {
            background-color: #45a049;
        }
        
        .btn-secondary {
            background-color: #2196F3;
            color: white;
        }
        
        .btn-secondary:hover {
            background-color: #1976D2;
        }
        
        .btn-warning {
            background-color: #FF9800;
            color: white;
        }
        
        .btn-warning:hover {
            background-color: #F57C00;
        }
        
        .status {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            display: none;
            white-space: pre-wrap;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            max-height: 400px;
            overflow-y: auto;
        }

        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .status.info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .response-container {
            margin-top: 20px;
            display: none;
        }

        .response-header {
            background-color: #f8f9fa;
            padding: 10px 15px;
            border-radius: 5px 5px 0 0;
            border: 1px solid #dee2e6;
            font-weight: bold;
            color: #495057;
        }

        .response-body {
            background-color: #ffffff;
            padding: 15px;
            border: 1px solid #dee2e6;
            border-top: none;
            border-radius: 0 0 5px 5px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
            word-break: break-all;
        }
        
        .info-box {
            background-color: #e3f2fd;
            border: 1px solid #2196F3;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 20px;
        }
        
        .info-box h3 {
            margin-top: 0;
            color: #1976D2;
        }
        
        .info-box ul {
            margin: 10px 0;
            padding-left: 20px;
        }
        
        .info-box li {
            margin: 5px 0;
        }
        
        .token-info {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 10px;
            margin-bottom: 15px;
            font-size: 14px;
        }
        
        .json-tools {
            display: flex;
            gap: 10px;
            margin-bottom: 10px;
        }
        
        .json-tools button {
            padding: 5px 15px;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 店小秘数据上传测试工具</h1>
        
        <div class="info-box">
            <h3>📋 使用说明</h3>
            <ul>
                <li><strong>目标API：</strong> https://www.dianxiaomi.com/api/popTemuProduct/add.json</li>
                <li><strong>处理流程：</strong> JSON → choiceSave.txt → ZIP压缩 → 上传</li>
                <li><strong>文件格式：</strong> form-data; name="file"; filename="blob"</li>
                <li><strong>参数：</strong> op=1, file=ZIP附件</li>
                <li><strong>认证：</strong> 自动获取店小秘网站的Cookie和Token</li>
                <li><strong>访问方式：</strong> 直接在浏览器中打开 test_upload.html 文件</li>
            </ul>
        </div>
        
        <div class="token-info">
            <strong>🔐 Token状态：</strong> <span id="tokenStatus">检测中...</span>
        </div>
        
        <div class="form-group">
            <label for="jsonData">JSON数据内容：</label>
            <div class="json-tools">
                <button type="button" class="btn-secondary" id="formatJsonBtn">格式化JSON</button>
                <button type="button" class="btn-secondary" id="compressJsonBtn">压缩JSON</button>
                <button type="button" class="btn-warning" id="loadSampleBtn">加载示例数据</button>
                <button type="button" class="btn-secondary" id="testZipBtn">测试ZIP创建</button>
            </div>
            <textarea id="jsonData" placeholder="请在此输入JSON数据..."></textarea>
        </div>
        
        <div class="button-group">
            <button type="button" class="btn-primary" id="uploadBtn">🚀 测试上传</button>
            <button type="button" class="btn-secondary" id="validateBtn">✅ 验证JSON</button>
            <button type="button" class="btn-warning" id="clearBtn">🗑️ 清空数据</button>
            <button type="button" class="btn-secondary" id="debugBtn">🔧 调试面板</button>
        </div>

        <div id="status" class="status"></div>

        <!-- 响应显示区域 -->
        <div id="responseContainer" class="response-container">
            <div class="response-header">
                📥 店小秘API响应结果
                <div style="float: right;">
                    <button type="button" id="copyResponseBtn" style="padding: 2px 8px; font-size: 12px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer;">复制</button>
                    <button type="button" id="hideResponseBtn" style="padding: 2px 8px; font-size: 12px; background: #6c757d; color: white; border: none; border-radius: 3px; cursor: pointer; margin-left: 5px;">关闭</button>
                </div>
                <div style="clear: both;"></div>
            </div>
            <div id="responseBody" class="response-body"></div>
        </div>

        <!-- 调试面板 -->
        <div id="debugPanel" style="display: none; margin-top: 20px;">
            <div class="info-box">
                <h3>🔧 调试信息</h3>
                <div class="button-group" style="margin-bottom: 15px;">
                    <button type="button" class="btn-secondary" id="showCookiesBtn">查看Cookies</button>
                    <button type="button" class="btn-secondary" id="testCorsBtn">测试CORS</button>
                    <button type="button" class="btn-secondary" id="downloadBtn">下载为文件</button>
                </div>
                <div id="debugOutput" style="background: #f8f9fa; padding: 10px; border-radius: 5px; font-family: monospace; font-size: 12px; max-height: 200px; overflow-y: auto;"></div>
            </div>
        </div>
    </div>


</body>
</html>
