# 🚀 增强版商品上传工具使用指南

## 🎯 解决的核心问题

您提出的问题非常准确：**test_upload.html 在普通浏览器环境中无法直接调用店小秘API，需要通过扩展的background.js来实现跨域请求**。

## 🏗️ 技术架构

### 问题分析
1. **test_upload.html** - 普通HTML页面，受浏览器同源策略限制
2. **跨域限制** - 无法直接访问店小秘域名下的Cookie和API
3. **权限不足** - 没有扩展的特殊权限

### 解决方案
```
test_upload_enhanced.html → chrome.runtime.sendMessage() → background.js → 店小秘API
                                                              ↓
                         ← 返回结果 ← content script ← 注入到店小秘页面
```

## 📁 新增文件

### 1. 增强版上传工具
**文件**: `test_upload_enhanced.html`
- 通过扩展API调用店小秘接口
- 自动检测扩展环境和连接状态
- 完整的商品上传表单

### 2. Background服务扩展
**文件**: `src/background/index.ts` (已扩展)
- 新增 `DianxiaomiBackgroundService` 类
- 支持所有店小秘API调用
- 自动查找店小秘标签页

### 3. Content Script处理器
**文件**: `src/content-scripts/dianxiaomi-api-handler.ts`
- 在店小秘页面中运行
- 处理来自background的API调用请求
- 自动获取认证信息

### 4. Manifest配置更新
**文件**: `manifest.config.ts` (已更新)
- 注册店小秘content script
- 添加域名权限
- 更新CSP策略

## 🔧 支持的API调用

### 1. Token状态检查
```javascript
chrome.runtime.sendMessage({
  action: 'GET_DIANXIAOMI_TOKEN_STATUS'
})
```

### 2. 获取店铺列表
```javascript
chrome.runtime.sendMessage({
  action: 'GET_DIANXIAOMI_SHOP_LIST'
})
```

### 3. 获取运费模板
```javascript
chrome.runtime.sendMessage({
  action: 'GET_FREIGHT_TEMPLATES',
  shopId: 'your_shop_id'
})
```

### 4. 获取商品分类
```javascript
chrome.runtime.sendMessage({
  action: 'GET_PRODUCT_CATEGORIES',
  shopId: 'your_shop_id'
})
```

### 5. 上传商品
```javascript
chrome.runtime.sendMessage({
  action: 'UPLOAD_PRODUCT',
  productData: {
    shopId: 'shop_id',
    title: '商品标题',
    description: '商品描述',
    price: 99.99,
    stock: 100
  }
})
```

### 6. 通用API调用
```javascript
chrome.runtime.sendMessage({
  action: 'CALL_DIANXIAOMI_API',
  apiConfig: {
    url: 'https://www.dianxiaomi.com/api/xxx',
    method: 'POST',
    data: { /* 请求数据 */ },
    headers: { /* 自定义头部 */ }
  }
})
```

## 📋 使用步骤

### 第一步：环境准备
1. **确保扩展已安装并启用**
2. **在店小秘网站登录**
   ```
   https://www.dianxiaomi.com
   ```
3. **保持店小秘标签页打开**

### 第二步：打开增强版工具
1. **打开文件**: `test_upload_enhanced.html`
2. **检查扩展状态**: 页面会自动检测扩展环境
3. **验证连接状态**: 确认与店小秘的连接正常

### 第三步：获取基础数据
1. **点击"🔑 获取Token状态"** - 验证登录状态
2. **点击"🏪 获取店铺列表"** - 获取可用店铺
3. **选择目标店铺** - 在下拉框中选择

### 第四步：上传商品
1. **填写商品信息**:
   - 商品标题
   - 商品描述
   - 商品价格
   - 库存数量
2. **点击"📤 上传商品"**
3. **查看上传结果**

## 🔍 调试功能

### 扩展状态检查
- ✅ 扩展环境可用
- ✅ 店小秘标签页已找到
- ✅ Token状态有效
- ✅ 用户信息正确

### 连接状态监控
- 实时显示连接状态
- 详细的错误信息
- 完整的调试日志

### API测试功能
- **🧪 测试API调用** - 测试基础API连通性
- **🔍 检查连接状态** - 重新检查所有连接
- **🗑️ 清除日志** - 清理调试信息

## ⚠️ 注意事项

### 1. 扩展环境要求
- 必须在扩展环境中运行
- 不能在普通浏览器页面中使用
- 需要扩展的特殊权限

### 2. 店小秘登录要求
- 必须在店小秘网站登录
- 保持店小秘标签页打开
- Token必须有效

### 3. 网络连接要求
- 稳定的网络连接
- 能够访问店小秘域名
- 没有代理或防火墙阻拦

## 🚨 常见问题

### 问题1: 扩展环境检测失败
**错误**: "❌ 扩展环境检测失败"
**解决**:
1. 确保在扩展环境中打开页面
2. 检查扩展是否正确安装
3. 重新加载扩展

### 问题2: 未找到店小秘标签页
**错误**: "❌ 未找到店小秘标签页"
**解决**:
1. 打开 https://www.dianxiaomi.com
2. 确保已登录
3. 保持标签页打开

### 问题3: Token状态无效
**错误**: "❌ Token状态无效"
**解决**:
1. 重新登录店小秘
2. 检查Cookie是否被清除
3. 刷新店小秘页面

### 问题4: API调用失败
**错误**: "❌ API调用失败"
**解决**:
1. 检查网络连接
2. 确认API地址正确
3. 查看详细错误信息

## 🔧 开发者调试

### Background Console
```javascript
// 在扩展的background页面控制台
console.log('Background service worker running');
```

### Content Script Console
```javascript
// 在店小秘页面控制台
console.log('Content script loaded');
```

### 测试页面Console
```javascript
// 在test_upload_enhanced.html控制台
console.log('Extension available:', typeof chrome !== 'undefined');
```

## 🎉 优势对比

### 原版 test_upload.html
- ❌ 受跨域限制
- ❌ 无法访问Cookie
- ❌ 无法调用API
- ❌ 功能受限

### 增强版 test_upload_enhanced.html
- ✅ 通过扩展绕过跨域
- ✅ 自动获取认证信息
- ✅ 完整API调用支持
- ✅ 实时状态监控
- ✅ 详细错误处理

## 🚀 扩展应用

### 1. 批量上传
可以基于此架构实现批量商品上传功能

### 2. 数据同步
可以实现与其他平台的数据同步

### 3. 自动化操作
可以开发自动化的商品管理工具

现在您可以使用增强版工具，通过扩展的background.js安全地调用店小秘API了！
