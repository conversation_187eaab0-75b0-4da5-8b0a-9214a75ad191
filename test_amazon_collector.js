// Amazon采集器功能测试脚本
// 此脚本可以直接在浏览器控制台中运行，测试Amazon采集功能

console.log('🧪 开始测试Amazon采集器功能...');

// 模拟Amazon搜索页面环境
function setupAmazonEnvironment() {
    // 修改URL以模拟Amazon搜索页面
    if (!window.location.href.includes('amazon.com/s')) {
        const newUrl = window.location.origin + window.location.pathname + '?amazon.com/s?i=garden&rh=n%3A1063308&fs=true&page=2';
        window.history.pushState({}, '', newUrl);
        console.log('✅ URL已修改为Amazon搜索页面格式:', window.location.href);
    }
}

// 创建批量采集按钮（右上角红色）
function createBatchCollectionButton() {
    // 移除已存在的按钮
    const existingButton = document.querySelector('.amazon-batch-collect-btn');
    if (existingButton) {
        existingButton.remove();
    }
    
    const button = document.createElement('button');
    button.className = 'amazon-batch-collect-btn';
    button.type = 'button';
    
    // 获取匹配的商品数量
    const productCount = document.querySelectorAll('[data-asin]:not(.amazon-collected)').length;
    
    button.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 9999;
        padding: 8px 16px;
        border-radius: 6px;
        border: none;
        background: #ff4d4f;
        color: white;
        cursor: pointer;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 14px;
        font-weight: 500;
        transition: all 0.3s ease;
        font-family: Arial, sans-serif;
    `;

    // 创建图标
    const icon = document.createElement('div');
    icon.innerHTML = `
        <svg width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
            <path d="M8 0a8 8 0 1 1 0 16A8 8 0 0 1 8 0zM4.5 7.5a.5.5 0 0 0 0 1h5.793l-2.147 2.146a.5.5 0 0 0 .708.708l3-3a.5.5 0 0 0 0-.708l-3-3a.5.5 0 1 0-.708.708L10.293 7.5H4.5z"/>
        </svg>
    `;
    button.appendChild(icon);

    // 创建文字和数量
    const textContainer = document.createElement('div');
    textContainer.style.cssText = `
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        line-height: 1.2;
    `;
    
    const mainText = document.createElement('span');
    mainText.textContent = '批量采集';
    mainText.style.fontSize = '14px';
    
    const countText = document.createElement('span');
    countText.className = 'product-count';
    countText.textContent = `${productCount} 个商品`;
    countText.style.cssText = `
        font-size: 12px;
        opacity: 0.9;
        font-weight: normal;
    `;
    
    textContainer.appendChild(mainText);
    textContainer.appendChild(countText);
    button.appendChild(textContainer);

    // 添加点击事件
    button.addEventListener('click', () => {
        alert(`开始批量采集 ${productCount} 个商品！`);
        console.log('🚀 批量采集功能已触发');
    });

    // 添加悬停效果
    button.addEventListener('mouseenter', () => {
        button.style.transform = 'scale(1.05)';
        button.style.boxShadow = '0 4px 12px rgba(255, 77, 79, 0.3)';
    });

    button.addEventListener('mouseleave', () => {
        button.style.transform = 'scale(1)';
        button.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.15)';
    });

    document.body.appendChild(button);
    console.log('✅ 批量采集按钮已创建，位置：右上角');
    return button;
}

// 为商品图片添加单品采集按钮
function addProductCollectionButtons() {
    const productElements = document.querySelectorAll('[data-asin]');
    let addedCount = 0;
    
    productElements.forEach(productElement => {
        // 避免重复添加
        if (productElement.querySelector('.amazon-product-collect-btn')) {
            return;
        }

        // 查找商品图片容器
        const imageContainer = productElement.querySelector('.s-product-image-container, .s-image-container');
        if (imageContainer) {
            // 设置图片容器为相对定位
            const container = imageContainer;
            container.style.position = 'relative';
            
            // 创建单品采集按钮
            const collectButton = document.createElement('div');
            collectButton.className = 'amazon-product-collect-btn';
            collectButton.style.cssText = `
                position: absolute;
                top: 8px;
                right: 8px;
                background: rgba(255, 77, 79, 0.9);
                color: white;
                padding: 6px 12px;
                border-radius: 4px;
                font-size: 12px;
                font-weight: 500;
                cursor: pointer;
                transition: all 0.3s ease;
                user-select: none;
                z-index: 10;
                opacity: 0;
                transform: translateY(-5px);
                backdrop-filter: blur(4px);
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
                font-family: Arial, sans-serif;
            `;
            collectButton.textContent = '单品采集';

            // 添加悬停效果
            collectButton.addEventListener('mouseenter', () => {
                collectButton.style.background = 'rgba(255, 77, 79, 1)';
                collectButton.style.transform = 'translateY(-5px) scale(1.05)';
            });

            collectButton.addEventListener('mouseleave', () => {
                collectButton.style.background = 'rgba(255, 77, 79, 0.9)';
                collectButton.style.transform = 'translateY(-5px) scale(1)';
            });

            // 添加点击事件
            collectButton.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                const asin = productElement.getAttribute('data-asin');
                alert(`开始采集商品 ASIN: ${asin}`);
                console.log('🎯 单品采集功能已触发，ASIN:', asin);
            });

            container.appendChild(collectButton);
            
            // 添加悬停效果显示/隐藏按钮
            container.addEventListener('mouseenter', () => {
                collectButton.style.opacity = '1';
                collectButton.style.transform = 'translateY(0)';
            });
            
            container.addEventListener('mouseleave', () => {
                collectButton.style.opacity = '0';
                collectButton.style.transform = 'translateY(-5px)';
            });
            
            addedCount++;
        }
    });
    
    console.log(`✅ 已为 ${addedCount} 个商品添加单品采集按钮`);
    return addedCount;
}

// 主测试函数
function runAmazonCollectorTest() {
    console.log('🔧 设置Amazon环境...');
    setupAmazonEnvironment();
    
    console.log('🔧 创建批量采集按钮...');
    createBatchCollectionButton();
    
    console.log('🔧 添加单品采集按钮...');
    const buttonCount = addProductCollectionButtons();
    
    console.log('✅ Amazon采集器功能测试完成！');
    console.log(`📊 测试结果：`);
    console.log(`   - 批量采集按钮：已创建（右上角红色按钮）`);
    console.log(`   - 单品采集按钮：已为 ${buttonCount} 个商品添加`);
    console.log(`   - 使用方法：鼠标悬停在商品图片上查看单品采集按钮`);
    
    // 显示成功通知
    const notification = document.createElement('div');
    notification.style.cssText = `
        position: fixed;
        top: 80px;
        right: 20px;
        z-index: 10000;
        padding: 12px 16px;
        border-radius: 6px;
        background: #52c41a;
        color: white;
        font-size: 14px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        font-family: Arial, sans-serif;
    `;
    notification.textContent = '✅ Amazon采集器功能测试完成！';
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.remove();
    }, 3000);
}

// 运行测试
runAmazonCollectorTest();
