// JSZip修复测试脚本

class JSZipTester {
    constructor() {
        this.init();
    }

    init() {
        this.log('JSZip修复测试工具已初始化');
        this.checkJSZipStatus();
    }

    checkJSZipStatus() {
        const jszipStatus = document.getElementById('jszipStatus');
        const jszipInfo = document.getElementById('jszipInfo');
        
        try {
            // 检查JSZip是否可用
            if (typeof JSZip !== 'undefined') {
                this.log('✅ JSZip库已在页面中可用');
                jszipStatus.className = 'status-card status-valid';
                jszipInfo.innerHTML = `
                    <div style="color: #52c41a;">
                        ✅ JSZip库检测成功<br>
                        版本: ${JSZip.version || '未知'}<br>
                        可以直接使用JSZip创建ZIP文件
                    </div>
                `;
            } else {
                this.log('⚠️ JSZip库不可用，需要动态加载');
                jszipStatus.className = 'status-card status-invalid';
                jszipInfo.innerHTML = `
                    <div style="color: #ff4d4f;">
                        ⚠️ JSZip库未检测到<br>
                        需要动态加载或在扩展环境中使用<br>
                        点击"测试JSZip可用性"尝试动态加载
                    </div>
                `;
            }
        } catch (error) {
            this.log('❌ JSZip检查失败: ' + error.message, 'error');
            jszipStatus.className = 'status-card status-invalid';
            jszipInfo.innerHTML = `
                <div style="color: #ff4d4f;">
                    ❌ JSZip检查失败<br>
                    错误: ${error.message}
                </div>
            `;
        }
    }

    async testJSZip() {
        try {
            this.log('开始测试JSZip可用性...');
            
            // 检查是否已经可用
            let JSZipLib = window.JSZip;
            
            if (!JSZipLib) {
                this.log('JSZip不可用，尝试动态加载...');
                
                // 动态加载JSZip
                const script = document.createElement('script');
                script.src = 'https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js';
                document.head.appendChild(script);
                
                // 等待脚本加载
                await new Promise((resolve, reject) => {
                    script.onload = resolve;
                    script.onerror = reject;
                    setTimeout(() => reject(new Error('加载超时')), 10000);
                });
                
                JSZipLib = window.JSZip;
                if (!JSZipLib) {
                    throw new Error('JSZip加载后仍不可用');
                }
                
                this.log('✅ JSZip库动态加载成功');
            }
            
            // 测试创建ZIP文件
            this.log('测试创建ZIP文件...');
            const zip = new JSZipLib();
            zip.file('test.txt', 'Hello, World!');
            
            const zipBlob = await zip.generateAsync({
                type: 'blob',
                compression: 'DEFLATE',
                compressionOptions: {
                    level: 6
                }
            });
            
            this.log(`✅ ZIP文件创建成功，大小: ${zipBlob.size} bytes`);
            
            // 更新状态显示
            const jszipStatus = document.getElementById('jszipStatus');
            const jszipInfo = document.getElementById('jszipInfo');
            
            jszipStatus.className = 'status-card status-valid';
            jszipInfo.innerHTML = `
                <div style="color: #52c41a;">
                    ✅ JSZip库测试成功<br>
                    版本: ${JSZipLib.version || '未知'}<br>
                    ZIP文件大小: ${zipBlob.size} bytes<br>
                    可以正常创建ZIP文件
                </div>
            `;
            
        } catch (error) {
            this.log('❌ JSZip测试失败: ' + error.message, 'error');
            
            const jszipStatus = document.getElementById('jszipStatus');
            const jszipInfo = document.getElementById('jszipInfo');
            
            jszipStatus.className = 'status-card status-invalid';
            jszipInfo.innerHTML = `
                <div style="color: #ff4d4f;">
                    ❌ JSZip测试失败<br>
                    错误: ${error.message}<br>
                    请检查网络连接或扩展环境
                </div>
            `;
        }
    }

    async testUpload() {
        try {
            this.log('开始测试上传流程...');
            
            // 检查扩展环境
            if (typeof chrome === 'undefined' || !chrome.runtime) {
                throw new Error('扩展环境不可用');
            }
            
            this.log('✅ 扩展环境可用');
            
            // 测试JSON数据
            const testJsonData = JSON.stringify({
                "attributes": "[{\"propName\":\"测试属性\",\"propValue\":\"测试值\"}]",
                "categoryId": "9938",
                "shopId": "6959965",
                "productName": "测试商品",
                "op": 1
            });
            
            this.log('准备测试数据: ' + testJsonData);
            
            // 发送上传请求到background
            this.log('发送上传请求到background...');
            
            const result = await new Promise((resolve, reject) => {
                chrome.runtime.sendMessage({
                    action: 'UPLOAD_DIANXIAOMI_PRODUCT',
                    jsonData: testJsonData
                }, (response) => {
                    if (chrome.runtime.lastError) {
                        reject(new Error(chrome.runtime.lastError.message));
                    } else {
                        resolve(response);
                    }
                });
            });
            
            if (result.success) {
                this.log('✅ 上传流程测试成功');
                this.log('响应结果: ' + JSON.stringify(result, null, 2));
            } else {
                this.log('❌ 上传流程测试失败: ' + result.error, 'error');
            }
            
        } catch (error) {
            this.log('❌ 上传流程测试异常: ' + error.message, 'error');
        }
    }

    log(message, level = 'info') {
        const logArea = document.getElementById('logArea');
        const timestamp = new Date().toLocaleTimeString();
        const levelIcon = level === 'error' ? '❌' : level === 'warn' ? '⚠️' : 'ℹ️';
        
        logArea.textContent += `[${timestamp}] ${levelIcon} ${message}\n`;
        logArea.scrollTop = logArea.scrollHeight;
        
        console.log(`[JSZipTester] ${message}`);
    }

    clearLog() {
        document.getElementById('logArea').textContent = '';
    }
}

// 全局实例
let jszipTester;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    jszipTester = new JSZipTester();
    
    // 绑定事件
    document.getElementById('testJSZipBtn').addEventListener('click', () => {
        jszipTester.testJSZip();
    });
    
    document.getElementById('testUploadBtn').addEventListener('click', () => {
        jszipTester.testUpload();
    });
    
    document.getElementById('clearLogBtn').addEventListener('click', () => {
        jszipTester.clearLog();
    });
});
