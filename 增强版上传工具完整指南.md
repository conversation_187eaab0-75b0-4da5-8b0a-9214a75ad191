# 🚀 增强版店小秘上传工具完整指南

## 🎯 完全借鉴test_upload.html的逻辑

根据您的要求，我已经完全借鉴了`test_upload.html`的验证功能和上传思路，通过扩展的background.js来实现跨域API调用。

## 🔧 核心功能对比

### 原版 test_upload.html
- ✅ JSON验证和格式化
- ✅ ZIP文件创建（choiceSave.txt）
- ✅ 店小秘API上传
- ✅ Token状态检查
- ❌ 受跨域限制

### 增强版 test_upload_enhanced.html
- ✅ 完全相同的JSON验证和格式化
- ✅ 完全相同的ZIP文件创建逻辑
- ✅ 完全相同的店小秘API上传流程
- ✅ 完全相同的Token状态检查
- ✅ 通过扩展绕过跨域限制

## 📋 借鉴的具体功能

### 1. JSON处理功能
```javascript
// 完全借鉴test_upload.html的逻辑
- 格式化JSON (formatJson)
- 压缩JSON (compressJson)  
- 验证JSON (validateJson)
- 加载示例数据 (loadSampleData)
```

### 2. ZIP文件创建
```javascript
// 完全按照test_upload.html的方式
const zip = new JSZip();
zip.file('choiceSave.txt', jsonData);
const zipBlob = await zip.generateAsync({
    type: 'blob',
    compression: 'DEFLATE',
    compressionOptions: { level: 6 }
});
```

### 3. 上传API调用
```javascript
// 完全按照test_upload.html的请求方式
const formData = new FormData();
formData.append('file', zipBlob, 'blob');
formData.append('op', '1');

fetch('https://www.dianxiaomi.com/api/popTemuProduct/add.json', {
    method: 'POST',
    body: formData,
    credentials: 'include',
    headers: {
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'zh-CN,zh;q=0.9',
        'Origin': 'https://www.dianxiaomi.com',
        'Referer': 'https://www.dianxiaomi.com/web/popTemu/edit',
        // ... 完全相同的headers
    }
});
```

### 4. Token状态检查
```javascript
// 完全借鉴test_upload.html的检查逻辑
- 检查Cookie存在性
- 调用userInfo.json API验证
- 显示用户信息和登录状态
- 错误处理和降级策略
```

## 🏗️ 技术架构

### 数据流向
```
test_upload_enhanced.html → 用户输入JSON
                          ↓
                         验证JSON格式
                          ↓
                    chrome.runtime.sendMessage()
                          ↓
                      background.js
                          ↓
                    查找店小秘标签页
                          ↓
                   content script / 注入脚本
                          ↓
                    创建ZIP文件 (choiceSave.txt)
                          ↓
                   调用店小秘API上传
                          ↓
                      返回结果
```

### 关键文件

#### 1. test_upload_enhanced.html
- 完全借鉴原版的界面布局
- 相同的JSON输入区域
- 相同的功能按钮

#### 2. test_upload_enhanced.js  
- 完全借鉴原版的所有验证逻辑
- 相同的JSON处理函数
- 相同的错误处理机制

#### 3. background/index.ts (扩展)
- `uploadDianxiaomiProduct()` - 主上传逻辑
- `createZipFile()` - ZIP文件创建
- `injectAndUploadProduct()` - 脚本注入上传

#### 4. content-scripts/dianxiaomi-api-handler.ts
- `uploadDianxiaomiProduct()` - 在店小秘页面中执行上传

## 📋 使用步骤

### 第一步：环境准备
1. **确保扩展已安装并启用**
2. **在店小秘网站登录**
   ```
   https://www.dianxiaomi.com
   ```
3. **保持店小秘标签页打开**

### 第二步：打开增强版工具
1. **打开文件**: `test_upload_enhanced.html`
2. **检查扩展状态**: 确认扩展环境可用
3. **验证Token状态**: 确认店小秘已登录

### 第三步：准备JSON数据
1. **点击"加载示例数据"** - 加载测试数据
2. **或手动输入JSON** - 输入您的商品数据
3. **点击"格式化JSON"** - 美化JSON格式
4. **点击"验证JSON"** - 确认格式正确

### 第四步：上传商品
1. **点击"🚀 测试上传"**
2. **查看上传进度** - 在日志区域监控
3. **查看上传结果** - 成功或失败信息

## 🔍 功能对比验证

### JSON处理功能
| 功能 | test_upload.html | test_upload_enhanced.html |
|------|------------------|---------------------------|
| 格式化JSON | ✅ | ✅ 完全相同 |
| 压缩JSON | ✅ | ✅ 完全相同 |
| 验证JSON | ✅ | ✅ 完全相同 |
| 示例数据 | ✅ | ✅ 完全相同 |

### 上传流程
| 步骤 | test_upload.html | test_upload_enhanced.html |
|------|------------------|---------------------------|
| Token检查 | ✅ | ✅ 通过background.js |
| ZIP创建 | ✅ | ✅ 完全相同逻辑 |
| API调用 | ✅ | ✅ 完全相同参数 |
| 错误处理 | ✅ | ✅ 完全相同机制 |

### 键盘快捷键
| 快捷键 | test_upload.html | test_upload_enhanced.html |
|--------|------------------|---------------------------|
| Ctrl+Enter | 上传 | ✅ 上传 |
| Ctrl+L | 加载示例 | ✅ 加载示例 |
| Ctrl+F | 格式化 | ✅ 格式化 |

## 🧪 测试验证

### 1. JSON验证测试
```json
// 使用相同的示例数据
{
  "attributes": "[{\"propName\":\"是否可用于食品接触\",\"refPid\":4010,\"pid\":1795,\"templatePid\":1261897,\"numberInputValue\":\"\",\"valueUnit\":\"\",\"vid\":\"67313\",\"propValue\":\"是\"}]",
  "categoryId": "9938",
  "shopId": "6959965",
  "productSemiManagedReq": "100",
  "sourceUrl": "https://www.amazon.com/dp/B0D2R2CGC1",
  "productName": "3in1 Cup Lid Brush MultiFunction Gap Cleaning Brush",
  "op": 1
}
```

### 2. 上传流程测试
1. **加载示例数据** → 验证JSON加载
2. **格式化JSON** → 验证格式化功能
3. **验证JSON** → 验证验证功能
4. **测试上传** → 验证完整上传流程

### 3. 错误处理测试
- 无效JSON → 相同的错误提示
- 未登录状态 → 相同的认证检查
- 网络错误 → 相同的错误处理

## ⚠️ 注意事项

### 1. 环境要求
- 必须在扩展环境中运行
- 需要店小秘网站登录
- 保持店小秘标签页打开

### 2. 功能限制
- ZIP文件创建依赖JSZip库
- API调用需要有效的Cookie
- 跨域限制通过扩展解决

### 3. 调试方法
- 查看浏览器控制台日志
- 使用工具内置的日志功能
- 检查扩展的background页面

## 🎉 总结

增强版上传工具完全借鉴了`test_upload.html`的：

1. ✅ **验证逻辑** - JSON格式验证、Token状态检查
2. ✅ **处理流程** - ZIP创建、FormData构建、API调用
3. ✅ **用户界面** - 相同的按钮布局和功能
4. ✅ **错误处理** - 相同的错误提示和处理机制
5. ✅ **快捷键支持** - 相同的键盘快捷键

唯一的区别是通过扩展的background.js来绕过跨域限制，实现了在任何环境下都能正常使用的店小秘商品上传功能！
