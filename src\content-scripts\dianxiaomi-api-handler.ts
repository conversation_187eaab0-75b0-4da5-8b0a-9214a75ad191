// 店小秘API处理content script
// 在店小秘页面中运行，处理来自background的API调用请求

console.info('[DianxiaomiAPIHandler] Content script loaded on:', window.location.href)

// API调用处理器
class DianxiaomiAPIHandler {
  constructor() {
    this.init()
  }

  init() {
    console.info('[DianxiaomiAPIHandler] 初始化API处理器')
    
    // 监听来自background的消息
    chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
      console.info('[DianxiaomiAPIHandler] 收到消息:', request)
      
      if (request.action === 'CALL_DIANXIAOMI_API') {
        this.handleAPICall(request.apiConfig)
          .then(result => {
            console.info('[DianxiaomiAPIHandler] API调用结果:', result)
            sendResponse(result)
          })
          .catch(error => {
            console.error('[DianxiaomiAPIHandler] API调用失败:', error)
            sendResponse({
              success: false,
              error: error.message || 'API调用失败'
            })
          })
        
        // 返回true表示异步响应
        return true
      }
      
      if (request.action === 'UPLOAD_DIANXIAOMI_PRODUCT') {
        // 处理店小秘商品上传请求
        this.uploadDianxiaomiProduct(request.jsonData)
          .then(result => {
            sendResponse(result)
          })
          .catch(error => {
            sendResponse({
              success: false,
              error: error.message || '上传失败'
            })
          })

        return true
      }

      if (request.action === 'GET_TEMU_INFO') {
        // 处理Temu信息获取（保持兼容性）
        this.getTemuInfo()
          .then(result => {
            sendResponse(result)
          })
          .catch(error => {
            sendResponse({
              success: false,
              error: error.message || '获取Temu信息失败'
            })
          })

        return true
      }

      return false
    })
  }

  // 处理API调用
  async handleAPICall(apiConfig: {
    url: string
    method?: string
    data?: any
    headers?: Record<string, string>
  }): Promise<{ success: boolean; data?: any; error?: string }> {
    try {
      console.info('[DianxiaomiAPIHandler] 处理API调用:', apiConfig.url)

      const defaultHeaders = {
        'Accept': 'application/json, text/plain, */*',
        'Content-Type': 'application/json',
        'X-Requested-With': 'XMLHttpRequest'
      }

      const headers = { ...defaultHeaders, ...apiConfig.headers }
      const method = apiConfig.method || 'GET'

      const fetchOptions: RequestInit = {
        method,
        credentials: 'include',
        headers
      }

      if (method !== 'GET' && apiConfig.data) {
        fetchOptions.body = JSON.stringify(apiConfig.data)
      }

      console.info('[DianxiaomiAPIHandler] 发送请求:', {
        url: apiConfig.url,
        method,
        headers,
        hasBody: !!fetchOptions.body
      })

      const response = await fetch(apiConfig.url, fetchOptions)
      
      console.info('[DianxiaomiAPIHandler] 响应状态:', response.status, response.statusText)

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const contentType = response.headers.get('content-type')
      let data: any

      if (contentType && contentType.includes('application/json')) {
        data = await response.json()
      } else {
        data = await response.text()
      }

      console.info('[DianxiaomiAPIHandler] 响应数据:', data)

      return {
        success: true,
        data
      }
    } catch (error) {
      console.error('[DianxiaomiAPIHandler] API调用失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'API调用失败'
      }
    }
  }

  // 上传店小秘商品 - 借鉴test_upload.html的完整逻辑
  async uploadDianxiaomiProduct(jsonData: string): Promise<{ success: boolean; data?: any; error?: string; status?: number; headers?: any }> {
    try {
      console.info('[DianxiaomiAPIHandler] 开始上传店小秘商品...')
      console.info('[DianxiaomiAPIHandler] JSON数据大小:', jsonData.length, 'bytes')

      // 检查JSZip是否可用
      const JSZip = (window as any).JSZip
      if (!JSZip) {
        throw new Error('JSZip库不可用，请确保页面已加载JSZip')
      }

      // 创建ZIP文件
      console.info('[DianxiaomiAPIHandler] 创建ZIP文件...')
      const zip = new JSZip()
      zip.file('choiceSave.txt', jsonData)

      const zipBlob = await zip.generateAsync({
        type: 'blob',
        compression: 'DEFLATE',
        compressionOptions: {
          level: 6
        }
      })

      console.info('[DianxiaomiAPIHandler] ZIP文件创建成功，大小:', zipBlob.size, 'bytes')

      // 创建FormData - 完全按照test_upload.html的方式
      const formData = new FormData()
      formData.append('file', zipBlob, 'blob')
      formData.append('op', '1')

      console.info('[DianxiaomiAPIHandler] 发送上传请求...')

      // 发送请求 - 完全按照test_upload.html的方式
      const response = await fetch('https://www.dianxiaomi.com/api/popTemuProduct/add.json', {
        method: 'POST',
        body: formData,
        credentials: 'include',
        headers: {
          'Accept': 'application/json, text/plain, */*',
          'Accept-Language': 'zh-CN,zh;q=0.9',
          'Origin': 'https://www.dianxiaomi.com',
          'Referer': 'https://www.dianxiaomi.com/web/popTemu/edit',
          'Sec-Fetch-Dest': 'empty',
          'Sec-Fetch-Mode': 'cors',
          'Sec-Fetch-Site': 'same-origin',
          'User-Agent': navigator.userAgent
        }
      })

      console.info('[DianxiaomiAPIHandler] 响应状态:', response.status, response.statusText)

      const responseText = await response.text()
      console.info('[DianxiaomiAPIHandler] 响应内容:', responseText)

      // 收集响应头
      const headers: Record<string, string> = {}
      for (const [key, value] of response.headers.entries()) {
        headers[key] = value
      }

      let responseData: any = responseText
      try {
        responseData = JSON.parse(responseText)
      } catch (e) {
        // 如果不是JSON，保持原文本
      }

      return {
        success: response.ok,
        data: responseData,
        status: response.status,
        headers,
        error: response.ok ? undefined : `HTTP ${response.status}: ${response.statusText}`
      }
    } catch (error) {
      console.error('[DianxiaomiAPIHandler] 上传失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '上传失败'
      }
    }
  }

  // 获取Temu信息（保持兼容性）
  async getTemuInfo(): Promise<{ success: boolean; data?: any; error?: string }> {
    try {
      console.info('[DianxiaomiAPIHandler] 获取Temu信息...')

      // 这里可以添加Temu特定的信息获取逻辑
      // 目前返回基本信息
      return {
        success: true,
        data: {
          url: window.location.href,
          title: document.title,
          timestamp: Date.now()
        }
      }
    } catch (error) {
      console.error('[DianxiaomiAPIHandler] 获取Temu信息失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '获取Temu信息失败'
      }
    }
  }

  // 获取页面中的anti-content头部（用于某些API）
  getAntiContent(): string | null {
    try {
      // 方法1: 从全局变量获取
      const win = window as any
      if (win.antiContent) return win.antiContent
      if (win.__INITIAL_STATE__?.antiContent) return win.__INITIAL_STATE__.antiContent

      // 方法2: 从脚本中搜索
      const scripts = Array.from(document.querySelectorAll('script'))
      for (const script of scripts) {
        const content = script.textContent || script.innerHTML
        const patterns = [
          /["']anti-content["']\s*:\s*["']([^"']+)["']/i,
          /antiContent\s*:\s*["']([^"']+)["']/i,
          /"anti-content":\s*"([^"]+)"/i
        ]

        for (const pattern of patterns) {
          const match = content.match(pattern)
          if (match) return match[1]
        }
      }
      return null
    } catch (error) {
      console.warn('[DianxiaomiAPIHandler] 获取anti-content失败:', error)
      return null
    }
  }

  // 获取页面中的CSRF token
  getCSRFToken(): string | null {
    try {
      // 从meta标签获取
      const metaToken = document.querySelector('meta[name="csrf-token"]') as HTMLMetaElement
      if (metaToken) return metaToken.content

      // 从表单中获取
      const hiddenToken = document.querySelector('input[name="_token"]') as HTMLInputElement
      if (hiddenToken) return hiddenToken.value

      // 从全局变量获取
      const win = window as any
      if (win.csrfToken) return win.csrfToken
      if (win._token) return win._token

      return null
    } catch (error) {
      console.warn('[DianxiaomiAPIHandler] 获取CSRF token失败:', error)
      return null
    }
  }

  // 检查是否在店小秘域名下
  isDianxiaomiDomain(): boolean {
    return window.location.hostname.includes('dianxiaomi.com')
  }

  // 获取当前用户信息（从页面中提取）
  getCurrentUserInfo(): any {
    try {
      const win = window as any
      
      // 尝试从全局变量获取用户信息
      if (win.userInfo) return win.userInfo
      if (win.__INITIAL_STATE__?.userInfo) return win.__INITIAL_STATE__.userInfo
      if (win.USER_INFO) return win.USER_INFO

      // 尝试从localStorage获取
      const storedUserInfo = localStorage.getItem('userInfo') || localStorage.getItem('user_info')
      if (storedUserInfo) {
        return JSON.parse(storedUserInfo)
      }

      return null
    } catch (error) {
      console.warn('[DianxiaomiAPIHandler] 获取用户信息失败:', error)
      return null
    }
  }

  // 获取页面状态信息
  getPageStatus(): {
    isLoggedIn: boolean
    userInfo: any
    antiContent: string | null
    csrfToken: string | null
    isDianxiaomiDomain: boolean
  } {
    return {
      isLoggedIn: !!this.getCurrentUserInfo(),
      userInfo: this.getCurrentUserInfo(),
      antiContent: this.getAntiContent(),
      csrfToken: this.getCSRFToken(),
      isDianxiaomiDomain: this.isDianxiaomiDomain()
    }
  }
}

// 只在店小秘域名下初始化
if (window.location.hostname.includes('dianxiaomi.com')) {
  console.info('[DianxiaomiAPIHandler] 在店小秘域名下，初始化API处理器')
  new DianxiaomiAPIHandler()
} else {
  console.info('[DianxiaomiAPIHandler] 不在店小秘域名下，跳过初始化')
}

export {}
