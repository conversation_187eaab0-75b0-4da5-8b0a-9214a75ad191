// 店小秘Token缓存服务
// 提供跨页面的token缓存和复用机制

import type { TokenStatus } from './dianxiaomiTokenService'
import { CookieHelper } from '../utils/cookieHelper'

// 缓存数据结构
interface CachedTokenData {
  tokenStatus: TokenStatus
  cookies: Record<string, string>
  sessionInfo?: {
    userInfo: any
    temuShops: any[]
    selectedShopId: string | null
    timestamp: number
  }
  timestamp: number
  version: string
}

// Token缓存服务类
class DianxiaomiTokenCache {
  private static instance: DianxiaomiTokenCache
  private readonly CACHE_KEY = 'dianxiaomi_token_cache'
  private readonly SESSION_KEY = 'dianxiaomi_session_cache'
  private readonly VERSION = '1.0.0'
  private readonly CACHE_DURATION = 30 * 60 * 1000 // 30分钟缓存

  private constructor() {}

  public static getInstance(): DianxiaomiTokenCache {
    if (!DianxiaomiTokenCache.instance) {
      DianxiaomiTokenCache.instance = new DianxiaomiTokenCache()
    }
    return DianxiaomiTokenCache.instance
  }

  // 保存token状态到缓存
  public saveTokenStatus(tokenStatus: TokenStatus): void {
    try {
      console.info('[TokenCache] 开始保存Token状态到缓存...')

      const cookies = this.extractCookies()
      const sessionInfo = this.extractSessionInfo()

      const cacheData: CachedTokenData = {
        tokenStatus,
        cookies,
        sessionInfo,
        timestamp: Date.now(),
        version: this.VERSION
      }

      // 保存到localStorage（持久化）
      const cacheString = JSON.stringify(cacheData)
      localStorage.setItem(this.CACHE_KEY, cacheString)
      console.info('[TokenCache] 已保存到localStorage，大小:', cacheString.length, '字符')

      // 保存到sessionStorage（会话级别）
      sessionStorage.setItem(this.SESSION_KEY, cacheString)
      console.info('[TokenCache] 已保存到sessionStorage')

      // 验证保存是否成功
      const savedData = localStorage.getItem(this.CACHE_KEY)
      if (savedData) {
        const parsedData = JSON.parse(savedData)
        console.info('[TokenCache] 保存验证成功，Cookie数量:', Object.keys(parsedData.cookies).length)
      }

      console.info('[TokenCache] Token状态已保存到缓存:', {
        isValid: tokenStatus.isValid,
        userAccount: tokenStatus.userInfo?.account,
        shopCount: tokenStatus.shopCount,
        cookieCount: Object.keys(cookies).length,
        cacheKey: this.CACHE_KEY
      })
    } catch (error) {
      console.error('[TokenCache] 保存token状态失败:', error)
    }
  }

  // 从缓存加载token状态
  public loadTokenStatus(): TokenStatus | null {
    try {
      // 优先从sessionStorage加载
      let cached = sessionStorage.getItem(this.SESSION_KEY)
      let source = 'sessionStorage'
      
      if (!cached) {
        // 降级到localStorage
        cached = localStorage.getItem(this.CACHE_KEY)
        source = 'localStorage'
      }

      if (!cached) {
        console.info('[TokenCache] 未找到缓存的token状态')
        return null
      }

      const cacheData: CachedTokenData = JSON.parse(cached)
      
      // 检查版本兼容性
      if (cacheData.version !== this.VERSION) {
        console.warn('[TokenCache] 缓存版本不兼容，清除缓存')
        this.clearCache()
        return null
      }

      // 检查缓存是否过期
      const isExpired = Date.now() - cacheData.timestamp > this.CACHE_DURATION
      if (isExpired) {
        console.info('[TokenCache] 缓存已过期，清除缓存')
        this.clearCache()
        return null
      }

      console.info(`[TokenCache] 从${source}加载token状态:`, {
        isValid: cacheData.tokenStatus.isValid,
        userAccount: cacheData.tokenStatus.userInfo?.account,
        cacheAge: Math.round((Date.now() - cacheData.timestamp) / 1000) + 's'
      })

      return cacheData.tokenStatus
    } catch (error) {
      console.error('[TokenCache] 加载token状态失败:', error)
      return null
    }
  }

  // 获取缓存的Cookie信息
  public getCachedCookies(): Record<string, string> | null {
    try {
      const cached = sessionStorage.getItem(this.SESSION_KEY) || 
                    localStorage.getItem(this.CACHE_KEY)
      
      if (!cached) return null

      const cacheData: CachedTokenData = JSON.parse(cached)
      
      // 检查缓存是否过期
      const isExpired = Date.now() - cacheData.timestamp > this.CACHE_DURATION
      if (isExpired) return null

      return cacheData.cookies
    } catch (error) {
      console.error('[TokenCache] 获取缓存Cookie失败:', error)
      return null
    }
  }

  // 获取缓存的会话信息
  public getCachedSessionInfo(): any | null {
    try {
      const cached = sessionStorage.getItem(this.SESSION_KEY) || 
                    localStorage.getItem(this.CACHE_KEY)
      
      if (!cached) return null

      const cacheData: CachedTokenData = JSON.parse(cached)
      
      // 检查缓存是否过期
      const isExpired = Date.now() - cacheData.timestamp > this.CACHE_DURATION
      if (isExpired) return null

      return cacheData.sessionInfo
    } catch (error) {
      console.error('[TokenCache] 获取缓存会话信息失败:', error)
      return null
    }
  }

  // 应用缓存的Cookie到当前页面
  public applyCachedCookies(): boolean {
    try {
      const cachedCookies = this.getCachedCookies()
      if (!cachedCookies) return false

      // 注意：由于浏览器安全限制，无法直接设置跨域Cookie
      // 这个方法主要用于检查和验证Cookie
      const currentCookies = this.extractCookies()
      const dxmCookieKeys = Object.keys(cachedCookies).filter(key =>
        key.includes('dxm_') || 
        key.includes('JSESSIONID') || 
        key.includes('MYJ_') ||
        key.includes('dianxiaomi')
      )

      console.info('[TokenCache] 缓存的店小秘Cookie:', dxmCookieKeys.length, '个')
      return dxmCookieKeys.length > 0
    } catch (error) {
      console.error('[TokenCache] 应用缓存Cookie失败:', error)
      return false
    }
  }

  // 清除所有缓存
  public clearCache(): void {
    try {
      localStorage.removeItem(this.CACHE_KEY)
      sessionStorage.removeItem(this.SESSION_KEY)
      console.info('[TokenCache] 所有缓存已清除')
    } catch (error) {
      console.error('[TokenCache] 清除缓存失败:', error)
    }
  }

  // 获取缓存统计信息
  public getCacheInfo(): {
    hasCache: boolean
    source: 'localStorage' | 'sessionStorage' | 'none'
    cacheAge: number
    isExpired: boolean
    version: string
    cookieCount: number
  } {
    try {
      let cached: string | null = null
      let source: 'localStorage' | 'sessionStorage' | 'none' = 'none'

      // 检查sessionStorage
      cached = sessionStorage.getItem(this.SESSION_KEY)
      if (cached) {
        source = 'sessionStorage'
      } else {
        // 检查localStorage
        cached = localStorage.getItem(this.CACHE_KEY)
        if (cached) {
          source = 'localStorage'
        }
      }

      if (!cached) {
        return {
          hasCache: false,
          source: 'none',
          cacheAge: 0,
          isExpired: true,
          version: this.VERSION,
          cookieCount: 0
        }
      }

      const cacheData: CachedTokenData = JSON.parse(cached)
      const cacheAge = Date.now() - cacheData.timestamp
      const isExpired = cacheAge > this.CACHE_DURATION

      return {
        hasCache: true,
        source,
        cacheAge,
        isExpired,
        version: cacheData.version,
        cookieCount: Object.keys(cacheData.cookies).length
      }
    } catch (error) {
      console.error('[TokenCache] 获取缓存信息失败:', error)
      return {
        hasCache: false,
        source: 'none',
        cacheAge: 0,
        isExpired: true,
        version: this.VERSION,
        cookieCount: 0
      }
    }
  }

  // 提取当前页面的Cookie
  private extractCookies(): Record<string, string> {
    try {
      console.info('[TokenCache] 开始提取Cookie信息...')

      // 使用Cookie辅助工具获取详细信息
      const cookieInfo = CookieHelper.getDetailedCookieInfo()
      const validation = CookieHelper.validateDianxiaomiCookies()

      console.info('[TokenCache] Cookie详细信息:', {
        domain: cookieInfo.domain,
        isOnDianxiaomiDomain: cookieInfo.isOnDianxiaomiDomain,
        cookieStringLength: cookieInfo.cookieString.length,
        totalCookies: cookieInfo.cookieStats.totalCount,
        dxmCookies: cookieInfo.cookieStats.dxmCount,
        dxmCookieNames: cookieInfo.cookieStats.dxmCookieNames,
        validationScore: validation.score,
        isValid: validation.isValid
      })

      // 返回所有Cookie（不仅仅是店小秘相关的）
      const allCookies = CookieHelper.getAllCookiesFromDocument()

      if (Object.keys(allCookies).length === 0) {
        console.warn('[TokenCache] 未获取到任何Cookie')
      } else {
        console.info('[TokenCache] 成功提取Cookie，总数:', Object.keys(allCookies).length)
      }

      return allCookies
    } catch (error) {
      console.error('[TokenCache] 提取Cookie失败:', error)
      return {}
    }
  }

  // 提取会话信息（如果在扩展环境中）
  private extractSessionInfo(): any | undefined {
    try {
      // 这里可以扩展为从chrome.storage.session获取信息
      // 但需要在扩展环境中才能使用
      if (typeof chrome !== 'undefined' && chrome.storage) {
        // 在扩展环境中可以获取更多信息
        return {
          timestamp: Date.now(),
          source: 'extension'
        }
      }
      return undefined
    } catch (error) {
      return undefined
    }
  }

  // 检查是否在店小秘域名下
  public isOnDianxiaomiDomain(): boolean {
    if (typeof window === 'undefined') return false
    return window.location.hostname.includes('dianxiaomi.com')
  }

  // 模拟token验证（用于测试页面）
  public async simulateTokenVerification(): Promise<TokenStatus> {
    try {
      // 首先尝试加载缓存
      const cachedStatus = this.loadTokenStatus()
      if (cachedStatus && cachedStatus.isValid) {
        console.info('[TokenCache] 使用缓存的token状态进行模拟验证')
        return cachedStatus
      }

      // 如果没有有效缓存，返回默认状态
      const defaultStatus: TokenStatus = {
        isValid: false,
        isLoggedIn: false,
        message: '未找到有效的token缓存，请在店小秘页面重新登录',
        lastCheckTime: Date.now()
      }

      return defaultStatus
    } catch (error) {
      console.error('[TokenCache] 模拟token验证失败:', error)
      return {
        isValid: false,
        isLoggedIn: false,
        message: `模拟验证失败: ${error instanceof Error ? error.message : '未知错误'}`,
        lastCheckTime: Date.now(),
        error: error instanceof Error ? error.message : '未知错误'
      }
    }
  }
}

// 导出单例实例
export const dianxiaomiTokenCache = DianxiaomiTokenCache.getInstance()

// 导出便捷方法
export const saveTokenToCache = (tokenStatus: TokenStatus) => 
  dianxiaomiTokenCache.saveTokenStatus(tokenStatus)

export const loadTokenFromCache = () => 
  dianxiaomiTokenCache.loadTokenStatus()

export const clearTokenCache = () => 
  dianxiaomiTokenCache.clearCache()

export const getTokenCacheInfo = () => 
  dianxiaomiTokenCache.getCacheInfo()

export const simulateTokenVerification = () => 
  dianxiaomiTokenCache.simulateTokenVerification()

export type { CachedTokenData }
